apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: statestore
spec:
  type: state.mysql
  version: v1
  metadata:
  - name: connectionString
    value: "root:123456@tcp(localhost:3306)/crawler_db?allowNativePasswords=true"
  - name: tableName
    value: "state"
  - name: schemaName
    value: "crawler_db"
  - name: pemPath
    value: ""
  - name: actorStateStore
    value: "true"
scopes:
- task-manager

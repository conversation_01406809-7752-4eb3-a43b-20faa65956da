apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: statestore-mongodb
spec:
  type: state.mongodb
  version: v1
  metadata:
  - name: host
    value: localhost:27017
  - name: databaseName
    value: crawler_state
  - name: collectionName
    value: spider_manager_state
  - name: queryIndexes
    value: '[{"key": {"name": 1}, "name": "nameIdx"}, {"key": {"id": 1}, "name": "idIdx"}]'
#  - name: unwrapValue
#    value: "true"
#  - name: unwrapKeys
#    value: "true"
#  - name: valueJsonStrictMode
#    value: "true"
#  - name: keyJsonStrictMode
#    value: "true"
#  - name: nestedObjectsAllowed
#    value: "true"


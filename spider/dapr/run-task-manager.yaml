version: 1
common:
  resourcesPath: ./components
  configFilePath: ./config.yaml
apps:
  - appID: task-manager
    appDirPath: ../
    appPort: 8080
    command: ["go", "run", "cmd/task_mgr/main.go"]
    env:
      SERVER_PORT: "8080"
      SERVER_HOST: "0.0.0.0"
      DAPR_APP_ID: "task-manager"
      DAPR_APP_PORT: "8080"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
    daprHTTPPort: 3500
    daprGRPCPort: 50001
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: false
    logLevel: info
    appLogDestination: console
    daprdLogDestination: console

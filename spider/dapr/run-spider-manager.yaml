version: 1
common:
  resourcesPath: ./components
  configFilePath: ./config.yaml
apps:
  - appID: spider-manager
    appDirPath: ../
    appPort: 8084
    command: ["./bin/spider-manager"]
    env:
      SERVER_PORT: "8084"
      SERVER_HOST: "0.0.0.0"
      DAPR_APP_ID: "spider-manager"
      DAPR_APP_PORT: "8084"
      DAPR_STATE_STORE: "statestore-mongodb"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
    daprHTTPPort: 3504
    daprGRPCPort: 50004
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: false
    logLevel: info
    appLogDestination: console
    daprdLogDestination: console 
version: 1
common:
  resourcesPath: ./components
  configFilePath: ./config.yaml

apps:
  # 爬虫任务管理服务
  - appID: task-manager
    appDirPath: ../
    appPort: 8080
    command: ["go", "run", "cmd/task_mgr/main.go"]
    env:
      # 服务器配置
      SERVER_PORT: "8080"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "task-manager"
      DAPR_APP_PORT: "8080"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 开发环境配置
      GIN_MODE: "debug"
      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3500
    daprGRPCPort: 50000
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6090
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

  # URL调度器服务
  - appID: url-scheduler
    appDirPath: ../
    appPort: 8081
    command: ["go", "run", "cmd/url_scheduler/main.go"]
    env:
      # 服务器配置
      SERVER_PORT: "8081"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "url-scheduler"
      DAPR_APP_PORT: "8081"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 开发环境配置
      GIN_MODE: "debug"
      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3501
    daprGRPCPort: 50001
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6091
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

  # 任务执行器服务
  - appID: task-executor
    appDirPath: ../
    appPort: 8082
    command: ["go", "run", "cmd/task_executor/main.go"]
    env:
      # 服务器配置
      SERVER_PORT: "8082"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "task-executor"
      DAPR_APP_PORT: "8082"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 开发环境配置
      GIN_MODE: "debug"
      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3502
    daprGRPCPort: 50002
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6092
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

  # Spider服务
  - appID: JSON_API-Spider
    appDirPath: ../
    appPort: 8083
    command: ["go", "run", "cmd/spider/main.go"]
    env:
      # 服务器配置
      SERVER_PORT: "8083"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "JSON_API-Spider"
      DAPR_APP_PORT: "8083"
      DAPR_STATE_STORE: "statestore-mongodb"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 开发环境配置
      GIN_MODE: "debug"
      LOG_LEVEL: "debug"
      
      # 爬虫配置
      DOWNLOADER_TYPE: "json_api"
      PARSER_TYPE: "json_api"

      # MongoDB配置
      MONGO_URI: "mongodb://localhost:27017"
      MONGO_DATABASE: "crawler_db"
      MONGO_COLLECTION: "json_api_data"
           
    # Dapr sidecar配置
    daprHTTPPort: 3503
    daprGRPCPort: 50003
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6093
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3


  # File-Spider服务
  - appID: File-Spider
    appDirPath: ../
    appPort: 8085
    command: ["go", "run", "cmd/spider/main.go"]
    env:
      # 服务器配置
      SERVER_PORT: "8085"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "File-Spider"
      DAPR_APP_PORT: "8085"
      DAPR_STATE_STORE: "statestore-mongodb"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 开发环境配置
      GIN_MODE: "debug"
      LOG_LEVEL: "debug"
      
      # 爬虫配置
      DOWNLOADER_TYPE: "html"
      PARSER_TYPE: "file"
      FILE_SAVE_DIR: "./downloads"

    # Dapr sidecar配置
    daprHTTPPort: 3511
    daprGRPCPort: 50011
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6095
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3
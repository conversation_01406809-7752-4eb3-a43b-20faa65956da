package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"golden_crawler/internal/services"
)

func main() {
	log.Println("启动任务执行服务...")

	// 从环境变量获取配置，如果没有则使用默认值
	taskManagerAppID := getEnv("TASK_MANAGER_APP_ID", "task-manager")
	pubsubName := getEnv("PUBSUB_NAME", "pubsub")
	topicName := getEnv("TOPIC_NAME", "url-download")

	// 创建任务执行服务
	executor, err := services.NewTaskExecutorService(taskManagerAppID, pubsubName, topicName)
	if err != nil {
		log.Fatalf("创建任务执行服务失败: %v", err)
	}
	defer executor.Close()

	// 启动服务
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	log.Println("正在启动任务执行服务...")
	if err := executor.Start(ctx); err != nil {
		log.Fatalf("启动任务执行服务失败: %v", err)
	}

	log.Println("任务执行服务已启动，等待Pubsub消息...")

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	<-sigChan
	log.Println("收到停止信号，正在关闭任务执行服务...")

	cancel()
	log.Println("任务执行服务已停止")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

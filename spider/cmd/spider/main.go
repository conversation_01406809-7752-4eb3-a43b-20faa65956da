package main

import (
	"context"
	"golden_crawler/internal/services"
	"log"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	log.Println("启动Spider服务...")

	// 创建Spider服务
	spiderService, err := services.NewSpiderService()
	if err != nil {
		log.Fatalf("创建Spider服务失败: %v", err)
	}

	// 创建上下文，用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置信号处理
	signalCh := make(chan os.Signal, 1)
	signal.Notify(signalCh, os.Interrupt, syscall.SIGTERM)

	// 启动服务
	go func() {
		if err := spiderService.Start(ctx); err != nil {
			log.Fatalf("启动Spider服务失败: %v", err)
		}
	}()

	// 等待终止信号
	<-signalCh
	log.Println("收到终止信号，正在关闭服务...")

	// 关闭服务
	if err := spiderService.Close(); err != nil {
		log.Printf("关闭Spider服务时发生错误: %v", err)
	}

	log.Println("Spider服务已成功关闭")
}

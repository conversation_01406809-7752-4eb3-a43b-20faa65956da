version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql-crawler
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: crawler_db
      MYSQL_USER: crawler_user
      MYSQL_PASSWORD: crawler_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-mysql.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 可选：phpMyAdmin 用于数据库管理
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin-crawler
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
    ports:
      - "8081:80"
    depends_on:
      - mysql

volumes:
  mysql_data:

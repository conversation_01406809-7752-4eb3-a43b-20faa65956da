FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -o spider ./cmd/spider/main.go

# 使用轻量级的alpine镜像
FROM alpine:latest

# 安装必要的CA证书，用于HTTPS请求
RUN apk --no-cache add ca-certificates

WORKDIR /app

# 从builder阶段复制编译好的应用
COPY --from=builder /app/spider .

# 设置环境变量
ENV SERVER_PORT=8083

# 暴露端口
EXPOSE 8083

# 运行应用
CMD ["./spider"] 
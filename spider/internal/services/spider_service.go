package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"golden_crawler/internal/entity/downloader"
	"golden_crawler/internal/entity/parser"
	"golden_crawler/internal/models"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/dapr/go-sdk/client"
	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
)

// SpiderService 爬虫服务
type SpiderService struct {
	daprClient client.Client
	httpPort   string
	downloader downloader.Downloader
	parser     parser.Parser
	httpClient *http.Client
	spiderName string // 存储爬虫名称
}

// NewSpiderService 创建爬虫服务
func NewSpiderService() (*SpiderService, error) {
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	// 从环境变量获取端口号
	port := os.Getenv("SERVER_PORT")
	if port == "" {
		return nil, fmt.Errorf("环境变量SERVER_PORT未配置，无法启动爬虫服务")
	}

	// 从环境变量获取爬虫名称, 爬虫名称就是Dapr的appID
	spiderName := os.Getenv("DAPR_APP_ID")
	if spiderName == "" {
		return nil, fmt.Errorf("环境变量DAPR_APP_ID未配置，无法启动爬虫服务")
	}

	// 从环境变量获取下载器类型
	downloaderType := os.Getenv("DOWNLOADER_TYPE")
	if downloaderType == "" {
		return nil, fmt.Errorf("环境变量DOWNLOADER_TYPE未配置，无法启动爬虫服务")
	}
	log.Printf("使用配置的下载器类型: %s", downloaderType)

	// 从环境变量获取解析器类型
	parserType := os.Getenv("PARSER_TYPE")
	if parserType == "" {
		return nil, fmt.Errorf("环境变量PARSER_TYPE未配置，无法启动爬虫服务")
	}
	log.Printf("使用配置的解析器类型: %s", parserType)

	// 创建下载器工厂并获取下载器
	downloaderFactory := downloader.NewDownloaderFactory("GoldenCrawler/1.0")
	downloader, err := downloaderFactory.GetDownloader(downloaderType)
	if err != nil {
		return nil, fmt.Errorf("创建下载器失败: %w", err)
	}

	// 创建解析器工厂并获取解析器
	parserFactory := parser.NewParserFactory()
	parser, err := parserFactory.GetParser(parserType)
	if err != nil {
		return nil, fmt.Errorf("创建解析器失败: %w", err)
	}

	// 创建HTTP客户端用于心跳更新
	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	return &SpiderService{
		daprClient: daprClient,
		httpPort:   port,
		downloader: downloader,
		parser:     parser,
		httpClient: httpClient,
		spiderName: spiderName,
	}, nil
}

// Start 启动爬虫服务
func (s *SpiderService) Start(ctx context.Context) error {
	log.Println("启动Spider服务...")

	// 创建Dapr HTTP服务
	daprSrv := daprd.NewService(":" + s.httpPort)

	// 注册crawl接口
	if err := daprSrv.AddServiceInvocationHandler("crawl", s.handleCrawl); err != nil {
		return fmt.Errorf("注册crawl接口失败: %w", err)
	}

	log.Printf("Spider服务已启动，监听端口: %s", s.httpPort)
	log.Println("已注册接口: crawl")

	// 启动心跳更新协程
	go s.updateHeartbeat(ctx)

	// 启动服务
	return daprSrv.Start()
}

// handleCrawl 处理爬取请求
func (s *SpiderService) handleCrawl(ctx context.Context, in *common.InvocationEvent) (*common.Content, error) {
	log.Printf("收到爬取请求: %s", string(in.Data))

	var urlItem models.URLQueueItem
	if err := json.Unmarshal(in.Data, &urlItem); err != nil {
		err = fmt.Errorf("解析URL数据失败: %w", err)
		log.Printf("%v", err)
		return nil, err
	}

	// 验证必要字段
	if urlItem.ID <= 0 {
		err := fmt.Errorf("无效的URL ID: %d", urlItem.ID)
		log.Printf("%v", err)
		return nil, err
	}

	if urlItem.URL == "" {
		err := fmt.Errorf("URL为空: ID=%d", urlItem.ID)
		log.Printf("%v", err)
		return nil, err
	}

	log.Printf("开始爬取URL: ID=%d, URL=%s, TaskID=%s", urlItem.ID, urlItem.URL, urlItem.TaskID)

	// 从URL所属的任务配置中获取超时时间, 失败的话就使用默认的30秒
	timeout := 30
	taskInfo, err := s.getTaskInfo(ctx, urlItem.TaskID)
	if err != nil {
		err = fmt.Errorf("获取任务信息失败: %w, 使用默认超时时间: %d秒", err, timeout)
		log.Printf("%v", err)
	} else if taskInfo != nil && taskInfo.Timeout > 0 {
		timeout = taskInfo.Timeout
	}

	result, err := s.downloader.Download(ctx, &urlItem, timeout)
	if err != nil {
		err = fmt.Errorf("下载失败: %w", err)
		log.Printf("%v", err)
		return nil, err
	}

	log.Printf("下载完成: ID=%d, 状态码=%d, 内容类型=%s, 大小=%d字节",
		urlItem.ID, result.StatusCode, result.ContentType, len(result.Content))

	// 检查状态码
	if result.StatusCode < 200 || result.StatusCode >= 300 {
		err = fmt.Errorf("下载失败: 状态码=%d", result.StatusCode)
		log.Printf("%v", err)
		return nil, err
	}

	// 解析内容
	parseConfig := ""
	if taskInfo != nil {
		parseConfig = taskInfo.ParseConfig
	}
	parseResult, err := s.parser.Parse(ctx, result.Content, &urlItem, parseConfig)
	if err != nil {
		err = fmt.Errorf("解析失败: %w", err)
		log.Printf("%v", err)
		return nil, err
	}

	log.Printf("解析完成: ID=%d, 提取URL数量=%d", urlItem.ID, len(parseResult.Items))

	// 将解析结果中的URL添加到提取的URL列表
	extractedURLs := []models.URLQueueItem{}
	for _, item := range parseResult.Items {
		if item != nil {
			extractedURLs = append(extractedURLs, *item)
		}
	}

	// 返回成功响应
	response := map[string]interface{}{
		"success":        true,
		"message":        "爬取成功",
		"url_id":         urlItem.ID,
		"extracted_urls": extractedURLs, // 添加提取的URL数组
	}

	responseData, err := json.Marshal(response)
	if err != nil {
		err = fmt.Errorf("序列化响应失败: %w", err)
		log.Printf("%v", err)
		return nil, err
	}

	return &common.Content{
		Data:        responseData,
		ContentType: "application/json",
		DataTypeURL: "",
	}, nil
}

// updateHeartbeat 定期更新爬虫心跳
func (s *SpiderService) updateHeartbeat(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := s.sendHeartbeat(ctx); err != nil {
				log.Printf("更新心跳失败: %v", err)
			}
		case <-ctx.Done():
			log.Println("心跳更新协程已停止")
			return
		}
	}
}

// sendHeartbeat 发送爬虫心跳
func (s *SpiderService) sendHeartbeat(ctx context.Context) error {
	// 创建更新爬虫请求
	updateReq := models.UpdateSpiderRequest{
		HeartbeatAt: time.Now(),
	}

	// 序列化请求
	data, err := json.Marshal(updateReq)
	if err != nil {
		return fmt.Errorf("序列化心跳数据失败: %w", err)
	}

	// 爬虫管理服务应用ID固定为spider-manager
	spiderManagerAppID := "spider-manager"
	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/spiders/%s", s.spiderName) // 用SpiderName更新
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", spiderManagerAppID, methodPath)

	// 设置超时上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(timeoutCtx, http.MethodPut, url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			return fmt.Errorf("更新心跳超时: %w", err)
		} else if timeoutCtx.Err() == context.Canceled {
			return fmt.Errorf("更新心跳请求被取消: %w", err)
		}
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("爬虫管理服务返回错误: 状态码=%d, 响应=%s", resp.StatusCode, string(respBody))
	}

	return nil
}

// getTaskInfo 通过Dapr调用获取任务信息
func (s *SpiderService) getTaskInfo(ctx context.Context, taskID string) (*models.CrawlerTask, error) {
	// 任务管理服务应用ID固定为task-mgr
	taskManagerAppID := "task-manager"
	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/tasks/%s", taskID)
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", taskManagerAppID, methodPath)

	// 设置超时上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(timeoutCtx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			return nil, fmt.Errorf("获取任务信息超时: %w", err)
		} else if timeoutCtx.Err() == context.Canceled {
			return nil, fmt.Errorf("获取任务信息请求被取消: %w", err)
		}
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("任务管理服务返回错误: 状态码=%d, 响应=%s", resp.StatusCode, string(respBody))
	}

	// 解析响应结构体
	var response struct {
		Success bool               `json:"success"`
		Data    models.CrawlerTask `json:"data"`
		Error   string             `json:"error"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析服务响应失败: %w 响应体=%s", err, string(respBody))
	}

	// 检查响应是否成功
	if !response.Success {
		return nil, fmt.Errorf("获取任务信息失败: %s", response.Error)
	}

	return &response.Data, nil
}

// Close 关闭服务
func (s *SpiderService) Close() error {
	// 关闭Dapr客户端
	if s.daprClient != nil {
		s.daprClient.Close()
	}

	log.Println("Spider服务已关闭")
	return nil
}

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"golden_crawler/config"
	"golden_crawler/internal/models"
	"log"
	"time"

	"github.com/dapr/go-sdk/client"
)

// SpiderManagerService 定义爬虫管理服务接口
type SpiderManagerService interface {
	// ListSpiders 获取爬虫列表，支持分页
	ListSpiders(ctx context.Context, limit, offset int) ([]*models.Spider, int, error)
	// GetSpider 根据名称获取爬虫详情
	GetSpider(ctx context.Context, name string) (*models.Spider, error)
	// UpdateSpider 更新爬虫信息
	UpdateSpider(ctx context.Context, name string, req *models.UpdateSpiderRequest) error
	// DeleteSpider 删除爬虫
	DeleteSpider(ctx context.Context, name string) error
	// RegisterSpider 注册爬虫
	RegisterSpider(ctx context.Context, req *models.CreateSpiderRequest) (*models.Spider, bool, error)
	// Close 关闭服务
	Close() error
}

// spiderManagerServiceImpl 爬虫管理服务实现
type spiderManagerServiceImpl struct {
	daprClient client.Client
	config     *config.Config
}

// NewSpiderManagerService 创建爬虫管理服务实例
func NewSpiderManagerService(cfg *config.Config) (SpiderManagerService, error) {
	// 创建Dapr客户端
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	return &spiderManagerServiceImpl{
		daprClient: daprClient,
		config:     cfg,
	}, nil
}

// findSpiderByName 根据名称查找爬虫
func (s *spiderManagerServiceImpl) findSpiderByName(ctx context.Context, name string) (bool, *models.Spider, error) {
	// 使用GetState直接获取爬虫
	spiderKey := fmt.Sprintf("spider:%s", name)
	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, spiderKey, nil)
	if err != nil {
		return false, nil, fmt.Errorf("查询爬虫失败: %w", err)
	}

	// 检查是否找到匹配的爬虫
	if result.Value == nil {
		return false, nil, nil
	}

	// 解析爬虫数据
	var spider models.Spider
	err = json.Unmarshal(result.Value, &spider)
	if err != nil {
		return false, nil, fmt.Errorf("解析爬虫数据失败: %w", err)
	}

	return true, &spider, nil
}

// RegisterSpider 注册爬虫
func (s *spiderManagerServiceImpl) RegisterSpider(ctx context.Context, req *models.CreateSpiderRequest) (*models.Spider, bool, error) {
	// 检查爬虫名称是否已存在
	exists, existingSpider, err := s.findSpiderByName(ctx, req.Name)
	if err != nil {
		return nil, false, fmt.Errorf("检查爬虫名称失败: %w", err)
	}

	if exists {
		return existingSpider, true, nil
	}

	// 创建爬虫对象
	now := time.Now()
	spider := &models.Spider{
		Name:         req.Name,
		Description:  req.Description,
		RegisteredAt: now,
		HeartbeatAt:  now,
	}

	// 保存爬虫对象
	spiderKey := fmt.Sprintf("spider:%s", req.Name)
	spiderData, err := json.Marshal(spider)
	if err != nil {
		return nil, false, fmt.Errorf("序列化爬虫数据失败: %w", err)
	}

	// 设置metadata指示这是一个JSON对象
	metadata := map[string]string{
		"contentType": "application/json",
	}
	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, spiderKey, spiderData, metadata)
	if err != nil {
		return nil, false, fmt.Errorf("保存爬虫到状态存储失败: %w", err)
	}

	log.Printf("成功注册爬虫: %s", req.Name)
	return spider, false, nil
}

// GetSpider 根据名称获取爬虫详情
func (s *spiderManagerServiceImpl) GetSpider(ctx context.Context, name string) (*models.Spider, error) {
	spiderKey := fmt.Sprintf("spider:%s", name)

	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, spiderKey, nil)
	if err != nil {
		return nil, fmt.Errorf("从状态存储获取爬虫失败: %w", err)
	}

	if result.Value == nil {
		return nil, fmt.Errorf("爬虫不存在: %s", name)
	}

	var spider models.Spider
	err = json.Unmarshal(result.Value, &spider)
	if err != nil {
		return nil, fmt.Errorf("解析爬虫数据失败: %w", err)
	}

	return &spider, nil
}

// UpdateSpider 更新爬虫信息
func (s *spiderManagerServiceImpl) UpdateSpider(ctx context.Context, name string, req *models.UpdateSpiderRequest) error {
	// 获取现有爬虫
	spider, err := s.GetSpider(ctx, name)
	if err != nil {
		return err
	}

	// 更新爬虫属性
	if req.Description != "" {
		spider.Description = req.Description
	}

	// 更新心跳时间
	if !req.HeartbeatAt.IsZero() {
		spider.HeartbeatAt = req.HeartbeatAt
	}

	// 保存更新后的爬虫
	spiderKey := fmt.Sprintf("spider:%s", name)
	spiderData, err := json.Marshal(spider)
	if err != nil {
		return fmt.Errorf("序列化爬虫数据失败: %w", err)
	}

	metadata := map[string]string{
		"contentType": "application/json",
	}
	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, spiderKey, spiderData, metadata)
	if err != nil {
		return fmt.Errorf("保存爬虫到状态存储失败: %w", err)
	}

	return nil
}

// DeleteSpider 删除爬虫
func (s *spiderManagerServiceImpl) DeleteSpider(ctx context.Context, name string) error {
	// 检查爬虫是否存在
	_, err := s.GetSpider(ctx, name)
	if err != nil {
		return err
	}

	// 删除爬虫
	spiderKey := fmt.Sprintf("spider:%s", name)
	err = s.daprClient.DeleteState(ctx, s.config.Dapr.StateStore, spiderKey, nil)
	if err != nil {
		return fmt.Errorf("删除爬虫失败: %w", err)
	}

	return nil
}

// ListSpiders 获取爬虫列表
func (s *spiderManagerServiceImpl) ListSpiders(ctx context.Context, limit, offset int) ([]*models.Spider, int, error) {
	// 使用QueryStateAlpha1直接查询所有爬虫 - 正确使用value.id字段路径
	query := fmt.Sprintf(`{
		"page": {
			"limit": %d,
			"offset": %d
		}
	}`, limit, offset)

	queryResponse, err := s.daprClient.QueryStateAlpha1(ctx, s.config.Dapr.StateStore, query, nil)
	if err != nil {
		return nil, 0, fmt.Errorf("查询爬虫列表失败: %w", err)
	}

	// 解析查询结果
	spiders := make([]*models.Spider, 0, len(queryResponse.Results))
	for _, item := range queryResponse.Results {
		var spider models.Spider
		if err := json.Unmarshal(item.Value, &spider); err != nil {
			log.Printf("解析爬虫数据失败: %v", err)
			continue
		}
		spiders = append(spiders, &spider)
	}

	// 获取总数
	total := 0
	// 使用不带分页的查询来获取总数
	countQuery := `{
	}`

	countResponse, err := s.daprClient.QueryStateAlpha1(ctx, s.config.Dapr.StateStore, countQuery, nil)
	if err != nil {
		// 如果获取总数失败，使用当前页数量作为退路
		log.Printf("获取爬虫总数失败: %v，使用当前页数量代替", err)
		total = len(spiders)
	} else {
		total = len(countResponse.Results)
	}

	return spiders, total, nil
}

// Close 关闭服务
func (s *spiderManagerServiceImpl) Close() error {
	if s.daprClient != nil {
		s.daprClient.Close()
	}
	return nil
}

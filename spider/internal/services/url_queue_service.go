package services

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"golden_crawler/config"
	"golden_crawler/internal/models"

	"github.com/dapr/go-sdk/client"
	_ "github.com/go-sql-driver/mysql"
)

// URLQueueService 定义URL队列服务接口
type URLQueueService interface {
	// 添加URL到队列
	AddURLs(ctx context.Context, taskID string, urls []models.ReqURL, priority models.TaskPriority) error

	// 获取待处理的URL
	GetPendingURLs(ctx context.Context, limit int) ([]*models.URLQueueItem, error)

	// 更新URL状态
	UpdateURLStatus(ctx context.Context, urlID int64, status models.URLStatus, errorMessage string) error

	// 获取任务的URL统计
	GetTaskURLStats(ctx context.Context, taskID string) (map[models.URLStatus]int, error)

	// 查询URL队列
	QueryURLs(ctx context.Context, query *models.URLQueueQuery) ([]*models.URLQueueItem, error)

	// 获取URL队列总数
	CountURLs(ctx context.Context, query *models.URLQueueQuery) (int, error)

	// 重置失败的URL为待处理状态
	ResetFailedURLs(ctx context.Context, taskID string, maxRetryCount int) (int64, error)

	// 重置处理中和已调度的URL为失败状态
	ResetProcessingURLs(ctx context.Context, taskID string) (int64, error)

	// 关闭数据库连接
	Close() error
}

// urlQueueServiceImpl URL队列服务实现
type urlQueueServiceImpl struct {
	db         *sql.DB
	config     *config.Config
	daprClient client.Client
}

// NewURLQueueService 创建新的URL队列服务实例
func NewURLQueueService(cfg *config.Config) (URLQueueService, error) {
	// 创建服务实例
	service := &urlQueueServiceImpl{
		config: cfg,
	}

	// 初始化Dapr客户端
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("初始化Dapr客户端失败: %w", err)
	}
	service.daprClient = daprClient

	// 构建MySQL连接字符串
	dsn, err := service.getDSN()
	if err != nil {
		return nil, fmt.Errorf("获取MySQL连接字符串失败: %w", err)
	}

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL数据库失败: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("MySQL数据库连接测试失败: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	service.db = db
	return service, nil
}

// getSecret 从Dapr的secretstore获取密钥
func (s *urlQueueServiceImpl) getSecret(key1, key2 string) (string, error) {
	// 构建密钥名称，使用冒号分隔嵌套键
	secretKey := fmt.Sprintf("%s_%s", key1, key2)

	// 从Dapr的secretstore获取密钥
	secret, err := s.daprClient.GetSecret(context.Background(), s.config.Dapr.SecretStore, secretKey, nil)
	if err != nil {
		return "", fmt.Errorf("从Dapr secretstore获取密钥失败: %w", err)
	}

	// 获取密钥值
	value, ok := secret[secretKey]
	if !ok {
		return "", fmt.Errorf("密钥 %s 不存在或格式不正确", secretKey)
	}

	return value, nil
}

// getDSN 构建数据库连接字符串
func (s *urlQueueServiceImpl) getDSN() (string, error) {
	// 尝试从Dapr secretstore获取数据库配置
	var user, password, dbName, host, port string
	var err error

	// 获取用户名
	user, err = s.getSecret("database", "username")
	if err != nil {
		return "", fmt.Errorf("无法获取数据库用户名: %w", err)
	}

	// 获取密码
	password, err = s.getSecret("database", "password")
	if err != nil {
		return "", fmt.Errorf("无法获取数据库密码: %w", err)
	}

	// 获取主机名
	host, err = s.getSecret("database", "host")
	if err != nil {
		return "", fmt.Errorf("无法获取数据库主机名: %w", err)
	}

	// 获取端口号
	port, err = s.getSecret("database", "port")
	if err != nil {
		return "", fmt.Errorf("无法获取数据库端口号: %w", err)
	}

	// 获取数据库名称
	dbName, err = s.getSecret("database", "database")
	if err != nil {
		return "", fmt.Errorf("无法获取数据库名称: %w", err)
	}

	// 构建DSN字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbName)

	log.Printf("数据库连接信息: 主机=%s, 端口=%s, 用户=%s, 数据库=%s\n",
		host, port, user, dbName)

	return dsn, nil
}

// AddURLs 添加URL到队列
func (s *urlQueueServiceImpl) AddURLs(ctx context.Context, taskID string, urls []models.ReqURL, priority models.TaskPriority) error {
	if len(urls) == 0 {
		return nil
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 准备插入语句
	stmt, err := tx.PrepareContext(ctx, `
		INSERT INTO url_queue (task_id, url, method, body, priority, page_type, status, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`)
	if err != nil {
		return fmt.Errorf("准备插入语句失败: %w", err)
	}
	defer stmt.Close()

	// 批量插入URL
	for _, url := range urls {
		_, err := stmt.ExecContext(ctx, taskID, url.URL, string(url.Method), url.Body, int(priority), url.PageType, string(models.URLStatusPending))
		if err != nil {
			return fmt.Errorf("插入URL失败: %w", err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	log.Printf("成功添加 %d 个URL到队列，任务ID: %s\n", len(urls), taskID)
	return nil
}

// getRunningTaskIDs 获取所有状态为running的任务ID
func (s *urlQueueServiceImpl) getRunningTaskIDs(ctx context.Context) ([]string, error) {
	// 先获取任务列表
	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, "task:list", nil)
	if err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	if result == nil || result.Value == nil {
		return []string{}, nil
	}

	// 解析任务ID列表
	var taskIDs []string
	if err := json.Unmarshal(result.Value, &taskIDs); err != nil {
		return nil, fmt.Errorf("解析任务列表失败: %w", err)
	}

	// 如果没有任务，直接返回空列表
	if len(taskIDs) == 0 {
		return []string{}, nil
	}

	// 构造任务键列表
	taskKeys := make([]string, len(taskIDs))
	for i, id := range taskIDs {
		taskKeys[i] = fmt.Sprintf("task:%s", id)
	}

	// 批量获取任务数据
	bulkItems, err := s.daprClient.GetBulkState(ctx, s.config.Dapr.StateStore, taskKeys, nil, 100)
	if err != nil {
		return nil, fmt.Errorf("批量获取任务状态失败: %w", err)
	}

	// 筛选出running状态的任务
	var runningTaskIDs []string
	for _, item := range bulkItems {
		if item.Error != "" {
			log.Printf("获取任务状态出错: %s", item.Error)
			continue
		}

		var task models.CrawlerTask
		if err := json.Unmarshal(item.Value, &task); err != nil {
			log.Printf("解析任务数据失败: %v", err)
			continue
		}

		// 只保留状态为running的任务ID
		if task.Status == models.StatusRunning {
			// 从键中提取任务ID，假设键格式为"task:{taskID}"
			taskID := strings.TrimPrefix(item.Key, "task:")
			runningTaskIDs = append(runningTaskIDs, taskID)
		}
	}

	return runningTaskIDs, nil
}

// GetPendingURLs 获取待处理的URL
func (s *urlQueueServiceImpl) GetPendingURLs(ctx context.Context, limit int) ([]*models.URLQueueItem, error) {
	// 获取所有running状态的任务ID
	runningTaskIDs, err := s.getRunningTaskIDs(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取运行中的任务ID失败: %w", err)
	}

	// 如果没有运行中的任务，直接返回空结果
	if len(runningTaskIDs) == 0 {
		return []*models.URLQueueItem{}, nil
	}

	// 构建SQL查询，只查询运行中任务的URL
	args := make([]interface{}, 0, len(runningTaskIDs)+2) // 多2个参数用于status和limit
	args = append(args, string(models.URLStatusPending))

	placeholders := make([]string, len(runningTaskIDs))
	for i := range runningTaskIDs {
		placeholders[i] = "?"
		args = append(args, runningTaskIDs[i])
	}

	args = append(args, limit)

	query := fmt.Sprintf(`
		SELECT id, task_id, url, method, body, priority, page_type, status, retry_count, error_message, created_at, updated_at
		FROM url_queue 
		WHERE status = ? AND task_id IN (%s)
		ORDER BY priority DESC, created_at ASC 
		LIMIT ?
	`, strings.Join(placeholders, ","))

	// 执行查询
	rows, err := s.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询待处理URL失败: %w", err)
	}
	defer rows.Close()

	var urls []*models.URLQueueItem
	for rows.Next() {
		url := &models.URLQueueItem{}
		var body, errorMessage, pageType sql.NullString

		err := rows.Scan(
			&url.ID, &url.TaskID, &url.URL, &url.Method, &body,
			&url.Priority, &pageType, &url.Status, &url.RetryCount, &errorMessage,
			&url.CreatedAt, &url.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描URL数据失败: %w", err)
		}

		if body.Valid {
			url.Body = body.String
		}
		if errorMessage.Valid {
			url.ErrorMessage = errorMessage.String
		}
		if pageType.Valid {
			url.PageType = pageType.String
		}

		urls = append(urls, url)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历URL数据失败: %w", err)
	}

	return urls, nil
}

// UpdateURLStatus 更新URL状态
func (s *urlQueueServiceImpl) UpdateURLStatus(ctx context.Context, urlID int64, status models.URLStatus, errorMessage string) error {
	var query string
	var args []interface{}

	if status == models.URLStatusFailed && errorMessage != "" {
		query = `
			UPDATE url_queue 
			SET status = ?, error_message = ?, retry_count = retry_count + 1, updated_at = NOW()
			WHERE id = ?
		`
		args = []interface{}{string(status), errorMessage, urlID}
	} else {
		query = `
			UPDATE url_queue 
			SET status = ?, updated_at = NOW()
			WHERE id = ?
		`
		args = []interface{}{string(status), urlID}
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("更新URL状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("URL状态更新行数为零: ID=%d", urlID)
	}

	return nil
}

// GetTaskURLStats 获取任务的URL统计
func (s *urlQueueServiceImpl) GetTaskURLStats(ctx context.Context, taskID string) (map[models.URLStatus]int, error) {
	query := `
		SELECT status, COUNT(*) as count
		FROM url_queue 
		WHERE task_id = ?
		GROUP BY status
	`

	rows, err := s.db.QueryContext(ctx, query, taskID)
	if err != nil {
		return nil, fmt.Errorf("查询任务URL统计失败: %w", err)
	}
	defer rows.Close()

	stats := make(map[models.URLStatus]int)
	for rows.Next() {
		var status string
		var count int

		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("扫描统计数据失败: %w", err)
		}

		stats[models.URLStatus(status)] = count
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历统计数据失败: %w", err)
	}

	return stats, nil
}

// QueryURLs 查询URL队列
func (s *urlQueueServiceImpl) QueryURLs(ctx context.Context, query *models.URLQueueQuery) ([]*models.URLQueueItem, error) {
	// 构建动态查询
	sqlQuery := `
		SELECT id, task_id, url, method, body, priority, page_type, status, retry_count, error_message, created_at, updated_at
		FROM url_queue WHERE 1=1
	`
	var args []interface{}

	if query.TaskID != "" {
		sqlQuery += " AND task_id = ?"
		args = append(args, query.TaskID)
	}

	if query.Status != "" {
		sqlQuery += " AND status = ?"
		args = append(args, string(query.Status))
	}

	if query.Priority > 0 {
		sqlQuery += " AND priority = ?"
		args = append(args, int(query.Priority))
	}

	if query.PageType != "" {
		sqlQuery += " AND page_type = ?"
		args = append(args, query.PageType)
	}

	sqlQuery += " ORDER BY created_at DESC"

	if query.Limit > 0 {
		sqlQuery += " LIMIT ?"
		args = append(args, query.Limit)

		if query.Offset > 0 {
			sqlQuery += " OFFSET ?"
			args = append(args, query.Offset)
		}
	}

	rows, err := s.db.QueryContext(ctx, sqlQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询URL队列失败: %w", err)
	}
	defer rows.Close()

	var urls []*models.URLQueueItem
	taskIDSet := make(map[string]struct{}) // 用于收集不重复的任务ID

	for rows.Next() {
		url := &models.URLQueueItem{}
		var body, errorMessage, pageType sql.NullString

		err := rows.Scan(
			&url.ID, &url.TaskID, &url.URL, &url.Method, &body,
			&url.Priority, &pageType, &url.Status, &url.RetryCount, &errorMessage,
			&url.CreatedAt, &url.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描URL数据失败: %w", err)
		}

		if body.Valid {
			url.Body = body.String
		}
		if errorMessage.Valid {
			url.ErrorMessage = errorMessage.String
		}
		if pageType.Valid {
			url.PageType = pageType.String
		}

		urls = append(urls, url)
		taskIDSet[url.TaskID] = struct{}{} // 记录不重复的任务ID
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历URL数据失败: %w", err)
	}

	// 如果没有URL，直接返回
	if len(urls) == 0 {
		return urls, nil
	}

	// 构建任务键列表，用于批量查询
	taskKeys := make([]string, 0, len(taskIDSet))
	for taskID := range taskIDSet {
		taskKeys = append(taskKeys, fmt.Sprintf("task:%s", taskID))
	}

	// 使用GetBulkState一次性查询所有任务信息
	taskNameMap := make(map[string]string) // 任务ID到任务名称的映射
	if len(taskKeys) > 0 {
		bulkItems, err := s.daprClient.GetBulkState(ctx, s.config.Dapr.StateStore, taskKeys, nil, 100)
		if err != nil {
			log.Printf("批量获取任务信息失败: %v", err)
		} else {
			for _, item := range bulkItems {
				if item.Error != "" {
					log.Printf("获取任务信息出错: %s", item.Error)
					continue
				}

				var task models.CrawlerTask
				if err := json.Unmarshal(item.Value, &task); err != nil {
					log.Printf("解析任务数据失败: %v", err)
					continue
				}

				// 从键中提取任务ID (格式为 "task:任务ID")
				taskID := strings.TrimPrefix(item.Key, "task:")
				taskNameMap[taskID] = task.Name
			}
		}
	}

	// 为每个URL设置任务名称
	for _, url := range urls {
		if name, exists := taskNameMap[url.TaskID]; exists {
			url.TaskName = name
		}
	}

	return urls, nil
}

// ResetFailedURLs 重置失败的URL为待处理状态
func (s *urlQueueServiceImpl) ResetFailedURLs(ctx context.Context, taskID string, maxRetryCount int) (int64, error) {
	query := `
		UPDATE url_queue 
		SET status = ?, error_message = '', retry_count = 0, updated_at = NOW()
		WHERE task_id = ? AND status = ? AND retry_count <= ?
	`

	result, err := s.db.ExecContext(ctx, query,
		string(models.URLStatusPending), taskID, string(models.URLStatusFailed), maxRetryCount)
	if err != nil {
		return 0, fmt.Errorf("重置失败的URL失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取影响行数失败: %w", err)
	}

	log.Printf("成功重置 %d 个失败的URL为待处理状态，任务ID: %s\n", rowsAffected, taskID)
	return rowsAffected, nil
}

// ResetProcessingURLs 重置处理中和已调度的URL为失败状态
func (s *urlQueueServiceImpl) ResetProcessingURLs(ctx context.Context, taskID string) (int64, error) {
	query := `
		UPDATE url_queue 
		SET status = ?, error_message = ?, updated_at = NOW()
		WHERE task_id = ? AND (status = ? OR status = ?)
	`

	result, err := s.db.ExecContext(ctx, query,
		string(models.URLStatusFailed), "人工放弃", taskID, string(models.URLStatusProcessing), string(models.URLStatusScheduled))
	if err != nil {
		return 0, fmt.Errorf("重置处理中和已调度的URL为失败状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取影响行数失败: %w", err)
	}

	log.Printf("成功重置 %d 个处理中和已调度的URL为失败状态，任务ID: %s\n", rowsAffected, taskID)
	return rowsAffected, nil
}

// CountURLs 获取URL队列总数
func (s *urlQueueServiceImpl) CountURLs(ctx context.Context, query *models.URLQueueQuery) (int, error) {
	// 构建动态查询
	sqlQuery := `
		SELECT COUNT(*) FROM url_queue WHERE 1=1
	`
	var args []interface{}

	if query.TaskID != "" {
		sqlQuery += " AND task_id = ?"
		args = append(args, query.TaskID)
	}

	if query.Status != "" {
		sqlQuery += " AND status = ?"
		args = append(args, string(query.Status))
	}

	if query.Priority > 0 {
		sqlQuery += " AND priority = ?"
		args = append(args, int(query.Priority))
	}

	if query.PageType != "" {
		sqlQuery += " AND page_type = ?"
		args = append(args, query.PageType)
	}

	var total int
	err := s.db.QueryRowContext(ctx, sqlQuery, args...).Scan(&total)
	if err != nil {
		return 0, fmt.Errorf("获取URL队列总数失败: %w", err)
	}

	return total, nil
}

// Close 关闭数据库连接
func (s *urlQueueServiceImpl) Close() error {
	var err error

	// 关闭数据库连接
	if s.db != nil {
		err = s.db.Close()
	}

	// 关闭Dapr客户端
	if s.daprClient != nil {
		s.daprClient.Close() // Dapr客户端的Close方法不返回错误
	}

	return err
}

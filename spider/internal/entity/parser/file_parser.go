package parser

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	dapr "github.com/dapr/go-sdk/client"

	"golden_crawler/internal/models"
)

// FileParser 实现用于将内容保存为文件的解析器
type FileParser struct {
	saveDir string
}

// NewFileParser 创建新的文件解析器实例
func NewFileParser() (*FileParser, error) {
	// 从环境变量获取保存目录
	saveDir := os.Getenv("FILE_SAVE_DIR")

	// 如果环境变量中没有，则从配置存储获取保存目录
	if saveDir == "" {
		// 创建Dapr客户端
		daprClient, err := dapr.NewClient()
		if err != nil {
			return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
		}
		defer daprClient.Close()

		saveDirConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "file_save_dir")
		if err != nil {
			return nil, fmt.Errorf("获取文件保存目录配置失败: %w", err)
		}
		if saveDirConfig == nil {
			return nil, fmt.Errorf("文件保存目录配置为空")
		}
		saveDir = saveDirConfig.Value
	}

	// 检查保存目录是否存在，不存在则创建
	if _, err := os.Stat(saveDir); os.IsNotExist(err) {
		if err := os.MkdirAll(saveDir, 0755); err != nil {
			return nil, fmt.Errorf("创建保存目录失败: %w", err)
		}
	}

	return &FileParser{
		saveDir: saveDir,
	}, nil
}

// Parse 实现Parser接口的Parse方法
func (p *FileParser) Parse(ctx context.Context, content []byte, urlItem *models.URLQueueItem, parseConfig string) (*ParseResult, error) {
	// 配置格式说明:
	// parseConfig应为JSON格式，每个页面类型对应一个数组配置，例如:
	// {
	//   "default": [
	//     {"filename": "example.html", "filepath": "/path/to/save/example.html"}
	//   ]
	// }
	// 其中，filename指定保存的文件名，filepath指定完整的保存路径(可选)
	// 如不指定filepath，则使用默认的保存目录(saveDir)和filename组合

	// 默认值（使用任务ID和时间戳）
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("%s_%s.dat", urlItem.TaskID, timestamp)
	filePath := filepath.Join(p.saveDir, filename)

	// 如果parseConfig不为空且urlItem中包含PageType，尝试解析配置
	if parseConfig != "" && urlItem.PageType != "" {
		var config map[string]interface{}
		if err := json.Unmarshal([]byte(parseConfig), &config); err == nil {
			// 检查是否存在与当前页面类型匹配的配置
			pageTypeValue, exists := config[urlItem.PageType]
			if !exists {
				// 页面类型不存在，使用默认配置
			} else {
				// 处理配置是数组的情况
				if configArray, isArray := pageTypeValue.([]interface{}); isArray && len(configArray) > 0 {
					// 使用数组中的第一个元素作为配置
					if firstConfig, isMap := configArray[0].(map[string]interface{}); isMap {
						// 如果配置中指定了filename，则使用配置的值
						configFilename, filenameOk := firstConfig["filename"].(string)
						if filenameOk && configFilename != "" {
							filename = configFilename
						}

						// 如果配置中指定了filepath，则使用配置的值
						configFilepath, filepathOk := firstConfig["filepath"].(string)
						if filepathOk && configFilepath != "" {
							filePath = configFilepath
						} else {
							// 只指定了filename但没有指定filepath时，使用默认saveDir和配置的filename
							if filenameOk && configFilename != "" {
								filePath = filepath.Join(p.saveDir, filename)
							}
						}
					}
				}
			}
		}
	}

	// 将内容写入文件
	err := os.WriteFile(filePath, content, 0644)
	if err != nil {
		return nil, fmt.Errorf("保存文件失败: %w", err)
	}

	// 返回结果，不生成新的URL队列项
	return &ParseResult{
		Items: []*models.URLQueueItem{},
		Data: map[string]interface{}{
			"file_path": filePath,
			"file_size": len(content),
		},
	}, nil
}

// Close 实现清理资源的方法（对于文件解析器，没有需要关闭的资源）
func (p *FileParser) Close(ctx context.Context) error {
	// 文件解析器没有需要关闭的资源
	return nil
}

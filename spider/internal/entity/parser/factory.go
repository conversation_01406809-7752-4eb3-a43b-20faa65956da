package parser

import (
	"fmt"
	"sync"
)

// 预定义解析器名称常量
const (
	JSONAPIParserName = "json_api"
	FileParserName    = "file"
)

// ParserFactory 解析器工厂，用于创建和管理解析器实例
type ParserFactory struct {
	parsers map[string]Parser
	mu      sync.RWMutex
}

// NewParserFactory 创建新的解析器工厂实例
func NewParserFactory() *ParserFactory {
	return &ParserFactory{
		parsers: make(map[string]Parser),
	}
}

// GetParser 获取指定名称的解析器实例
func (f *ParserFactory) GetParser(name string) (Parser, error) {
	f.mu.RLock()
	parser, exists := f.parsers[name]
	f.mu.RUnlock()

	if exists {
		return parser, nil
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	// 再次检查，避免并发创建
	parser, exists = f.parsers[name]
	if exists {
		return parser, nil
	}

	// 根据名称创建对应的解析器
	switch name {
	case JSONAPIParserName:
		// 注意：集合名称现在通过环境变量或配置存储设置
		jsonParser, err := NewJSONAPIParser()
		if err != nil {
			return nil, err
		}
		f.parsers[name] = jsonParser
		return jsonParser, nil
	case FileParserName:
		fileParser, err := NewFileParser()
		if err != nil {
			return nil, err
		}
		f.parsers[name] = fileParser
		return fileParser, nil
	default:
		return nil, fmt.Errorf("未知的解析器类型: %s", name)
	}
}

// Close 关闭所有解析器连接
func (f *ParserFactory) Close() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	var lastErr error
	for name, parser := range f.parsers {
		if closer, ok := parser.(interface {
			Close(ctx interface{}) error
		}); ok {
			if err := closer.Close(nil); err != nil {
				lastErr = fmt.Errorf("关闭解析器 %s 失败: %w", name, err)
			}
		}
	}

	// 清空解析器映射
	f.parsers = make(map[string]Parser)
	return lastErr
}

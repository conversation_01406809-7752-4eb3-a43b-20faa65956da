package parser

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"golden_crawler/internal/models"
)

// JSONAPIParser 实现用于解析JSON API的解析器
type JSONAPIParser struct {
	mongoClient     *mongo.Client
	mongoCollection *mongo.Collection
}

// NewJSONAPIParser 创建新的JSON API解析器实例
func NewJSONAPIParser() (*JSONAPIParser, error) {
	// 创建Dapr客户端
	daprClient, err := dapr.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}
	defer daprClient.Close()

	// 从环境变量获取MongoDB连接参数
	mongoURI := os.Getenv("MONGO_URI")
	databaseName := os.Getenv("MONGO_DATABASE")
	collectionName := os.Getenv("MONGO_COLLECTION")

	// 如果环境变量中没有，则从配置存储获取MongoDB连接参数
	if mongoURI == "" {
		mongoURIConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_uri")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB URI配置失败: %w", err)
		}
		if mongoURIConfig == nil {
			return nil, fmt.Errorf("MongoDB URI配置为空")
		}
		mongoURI = mongoURIConfig.Value
	}

	if databaseName == "" {
		databaseConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_database")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB数据库名称配置失败: %w", err)
		}
		if databaseConfig == nil {
			return nil, fmt.Errorf("MongoDB数据库名称配置为空")
		}
		databaseName = databaseConfig.Value
	}

	// 检查是否设置了集合名称
	if collectionName == "" {
		collectionConfig, err := daprClient.GetConfigurationItem(context.Background(), "configstore", "mongo_collection")
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB集合名称配置失败: %w", err)
		}
		if collectionConfig == nil {
			return nil, fmt.Errorf("MongoDB集合名称配置为空")
		}
		collectionName = collectionConfig.Value
	}

	// 创建MongoDB客户端
	clientOptions := options.Client().ApplyURI(mongoURI)
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return nil, fmt.Errorf("连接MongoDB失败: %w", err)
	}

	// 检查连接
	err = client.Ping(context.Background(), nil)
	if err != nil {
		return nil, fmt.Errorf("MongoDB连接测试失败: %w", err)
	}

	collection := client.Database(databaseName).Collection(collectionName)

	return &JSONAPIParser{
		mongoClient:     client,
		mongoCollection: collection,
	}, nil
}

// Parse 实现Parser接口的Parse方法
func (p *JSONAPIParser) Parse(ctx context.Context, content []byte, urlItem *models.URLQueueItem, parseConfig string) (*ParseResult, error) {
	// 检查JSON是否合法
	var jsonData interface{}
	if err := json.Unmarshal(content, &jsonData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}

	// 保存到MongoDB
	_, err := p.mongoCollection.InsertOne(ctx, map[string]interface{}{
		"task_id":    urlItem.TaskID,
		"data":       jsonData,
		"created_at": time.Now().Format(time.RFC3339),
	})

	if err != nil {
		return nil, fmt.Errorf("保存到MongoDB失败: %w", err)
	}

	// 这个解析器不会生成新的URL队列项
	return &ParseResult{
		Items: []*models.URLQueueItem{},
		Data:  jsonData,
	}, nil
}

// Close 关闭MongoDB连接
func (p *JSONAPIParser) Close(ctx context.Context) error {
	if p.mongoClient != nil {
		return p.mongoClient.Disconnect(ctx)
	}
	return nil
}

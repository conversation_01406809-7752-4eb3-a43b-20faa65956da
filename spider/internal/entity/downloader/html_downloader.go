package downloader

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"golden_crawler/internal/models"
)

// HTMLDownloader 实现用于下载普通静态网页的下载器
type HTMLDownloader struct {
	client    *http.Client
	userAgent string
}

// NewHTMLDownloader 创建新的HTML下载器实例
func NewHTMLDownloader(userAgent string) *HTMLDownloader {
	return &HTMLDownloader{
		client: &http.Client{
			Timeout: 30 * time.Second, // 默认超时时间
		},
		userAgent: userAgent,
	}
}

// Download 实现Downloader接口的Download方法
func (d *HTMLDownloader) Download(ctx context.Context, urlItem *models.URLQueueItem, timeout int) (*DownloadResult, error) {
	// 创建带有超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	defer cancel()

	startTime := time.Now()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(timeoutCtx, "GET", urlItem.URL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", d.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 发送请求
	resp, err := d.client.Do(req)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			return nil, fmt.Errorf("请求超时: %w", err)
		}
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	// 提取响应头
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	downloadTime := time.Since(startTime)

	// 返回下载结果
	return &DownloadResult{
		Content:      content,
		ContentType:  resp.Header.Get("Content-Type"),
		StatusCode:   resp.StatusCode,
		Headers:      headers,
		DownloadTime: downloadTime,
	}, nil
}

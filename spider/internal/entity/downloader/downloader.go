package downloader

import (
	"context"
	"time"

	"golden_crawler/internal/models"
)

// DownloadResult 定义下载结果
type DownloadResult struct {
	Content      []byte            // 下载的内容
	ContentType  string            // 内容类型
	StatusCode   int               // HTTP状态码
	Headers      map[string]string // HTTP响应头
	DownloadTime time.Duration     // 下载耗时
}

// Downloader 定义下载器接口
type Downloader interface {
	// Download 下载指定URL的内容
	// urlItem: URL队列项
	// timeout: 超时时间（秒）
	// 返回下载结果和错误
	Download(ctx context.Context, urlItem *models.URLQueueItem, timeout int) (*DownloadResult, error)
}

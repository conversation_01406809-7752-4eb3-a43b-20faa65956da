package downloader

import (
	"fmt"
	"sync"
)

// 预定义下载器名称常量
const (
	APIDownloaderName  = "json_api"
	HTMLDownloaderName = "html"
)

// DownloaderFactory 下载器工厂，用于创建和管理下载器实例
type DownloaderFactory struct {
	downloaders map[string]Downloader
	mu          sync.RWMutex
	userAgent   string
}

// NewDownloaderFactory 创建新的下载器工厂实例
func NewDownloaderFactory(userAgent string) *DownloaderFactory {
	return &DownloaderFactory{
		downloaders: make(map[string]Downloader),
		userAgent:   userAgent,
	}
}

// GetDownloader 获取指定名称的下载器实例
func (f *DownloaderFactory) GetDownloader(name string) (Downloader, error) {
	f.mu.RLock()
	downloader, exists := f.downloaders[name]
	f.mu.RUnlock()

	if exists {
		return downloader, nil
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	// 再次检查，避免并发创建
	downloader, exists = f.downloaders[name]
	if exists {
		return downloader, nil
	}

	// 根据名称创建对应的下载器
	switch name {
	case APIDownloaderName:
		apiDownloader := NewAPIDownloader(f.userAgent)
		f.downloaders[name] = apiDownloader
		return apiDownloader, nil
	case HTMLDownloaderName:
		htmlDownloader := NewHTMLDownloader(f.userAgent)
		f.downloaders[name] = htmlDownloader
		return htmlDownloader, nil
	default:
		return nil, fmt.Errorf("未知的下载器类型: %s", name)
	}
}

// Close 关闭所有下载器连接
func (f *DownloaderFactory) Close() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 清空下载器映射
	f.downloaders = make(map[string]Downloader)
	return nil
}

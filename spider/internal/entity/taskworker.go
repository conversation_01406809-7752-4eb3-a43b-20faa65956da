package entity

import (
	"context"
	"sync"
	"time"
)

// TaskFunc 定义任务函数类型
type TaskFunc func(ctx context.Context)

// TaskWorker 任务工作器，支持并发执行任务
type TaskWorker struct {
	// 并发数
	concurrency int
	// 任务队列channel
	taskChan chan TaskFunc
	// 用于停止工作器的context
	ctx    context.Context
	cancel context.CancelFunc
	// 等待所有worker协程结束的WaitGroup
	wg sync.WaitGroup
	// 标记是否已经启动
	started bool
	// 保护started字段的互斥锁
	mu sync.RWMutex
}

// NewTaskWorker 创建新的任务工作器
// concurrency: 并发协程数量
// bufferSize: 任务队列缓冲区大小，如果为0则使用无缓冲channel
func NewTaskWorker(concurrency int, bufferSize int) *TaskWorker {
	if concurrency <= 0 {
		concurrency = 1
	}

	ctx, cancel := context.WithCancel(context.Background())

	var taskChan chan TaskFunc
	if bufferSize > 0 {
		taskChan = make(chan TaskFunc, bufferSize)
	} else {
		taskChan = make(chan TaskFunc)
	}

	return &TaskWorker{
		concurrency: concurrency,
		taskChan:    taskChan,
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start 启动工作器，开始处理任务
func (tw *TaskWorker) Start() {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	if tw.started {
		return
	}

	tw.started = true

	// 启动指定数量的worker协程
	for i := 0; i < tw.concurrency; i++ {
		tw.wg.Add(1)
		go tw.worker(i)
	}
}

// AddTask 添加任务到队列
// task: 要执行的任务函数
// 返回bool表示是否成功添加（如果工作器已停止则返回false）
func (tw *TaskWorker) AddTask(task TaskFunc) bool {
	if task == nil {
		return false
	}

	select {
	case tw.taskChan <- task:
		return true
	case <-tw.ctx.Done():
		return false
	}
}

// AddTaskWithTimeout 添加任务到队列，带超时
// task: 要执行的任务函数
// timeout: 超时时间
// 返回bool表示是否成功添加
func (tw *TaskWorker) AddTaskWithTimeout(task TaskFunc, timeout time.Duration) bool {
	if task == nil {
		return false
	}

	select {
	case tw.taskChan <- task:
		return true
	case <-tw.ctx.Done():
		return false
	case <-time.After(timeout):
		return false
	}
}

// Stop 停止工作器
// 会等待所有正在执行的任务完成，然后关闭所有worker协程
func (tw *TaskWorker) Stop() {
	tw.mu.Lock()
	if !tw.started {
		tw.mu.Unlock()
		return
	}
	tw.mu.Unlock()

	// 取消context，通知所有worker停止接收新任务
	tw.cancel()

	// 关闭任务channel，让worker协程能够退出
	close(tw.taskChan)

	// 等待所有worker协程结束
	tw.wg.Wait()

	tw.mu.Lock()
	tw.started = false
	tw.mu.Unlock()
}

// IsRunning 检查工作器是否正在运行
func (tw *TaskWorker) IsRunning() bool {
	tw.mu.RLock()
	defer tw.mu.RUnlock()
	return tw.started
}

// GetConcurrency 获取并发数
func (tw *TaskWorker) GetConcurrency() int {
	return tw.concurrency
}

// GetQueueLength 获取当前队列中等待处理的任务数量
func (tw *TaskWorker) GetQueueLength() int {
	return len(tw.taskChan)
}

// worker 工作协程，负责从channel中读取并执行任务
func (tw *TaskWorker) worker(workerID int) {
	defer tw.wg.Done()

	for {
		select {
		case task, ok := <-tw.taskChan:
			if !ok {
				// channel已关闭，退出worker
				return
			}

			// 执行任务，使用recover防止panic导致worker退出
			func() {
				defer func() {
					if r := recover(); r != nil {
						// 可以在这里添加日志记录panic信息
						// log.Printf("Worker %d: task panic recovered: %v", workerID, r)
					}
				}()
				task(tw.ctx)
			}()

		case <-tw.ctx.Done():
			// 收到停止信号，但继续处理剩余任务直到channel关闭
			continue
		}
	}
}

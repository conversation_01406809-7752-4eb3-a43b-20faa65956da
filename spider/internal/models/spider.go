package models

import (
	"time"
)

// Spider 定义爬虫对象结构
type Spider struct {
	Name         string    `json:"name"`          // 爬虫名称
	Description  string    `json:"description"`   // 爬虫描述
	RegisteredAt time.Time `json:"registered_at"` // 注册时间
	HeartbeatAt  time.Time `json:"heartbeat_at"`  // 心跳时间
}

// CreateSpiderRequest 定义创建爬虫的请求结构
type CreateSpiderRequest struct {
	Name        string `json:"name" binding:"required"` // 爬虫名称
	Description string `json:"description"`             // 爬虫描述
}

// UpdateSpiderRequest 定义更新爬虫的请求结构
type UpdateSpiderRequest struct {
	Description string    `json:"description"`  // 爬虫描述
	HeartbeatAt time.Time `json:"heartbeat_at"` // 心跳时间
}

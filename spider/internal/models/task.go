package models

import (
	"time"
)

// HTTPMethod 定义HTTP请求方法
type HTTPMethod string

const (
	GET  HTTPMethod = "GET"
	POST HTTPMethod = "POST"
)

// ReqURL 定义初始化URL结构
type ReqURL struct {
	URL      string     `json:"url" binding:"required"`
	Method   HTTPMethod `json:"method" binding:"required"`
	PageType string     `json:"page_type"`      // 页面类型，用于区分不同类型的页面
	Body     string     `json:"body,omitempty"` // 仅当Method为POST时使用
}

// TaskPriority 定义任务优先级
type TaskPriority int

const (
	LowPriority    TaskPriority = 1
	MediumPriority TaskPriority = 2
	HighPriority   TaskPriority = 3
)

// TaskStatus 定义任务状态
type TaskStatus string

const (
	StatusPending   TaskStatus = "pending"
	StatusRunning   TaskStatus = "running"
	StatusCompleted TaskStatus = "completed"
	StatusFailed    TaskStatus = "failed"
	StatusPaused    TaskStatus = "paused"
	StatusDeleted   TaskStatus = "deleted"
)

// CrawlerTask 定义爬虫任务结构
type CrawlerTask struct {
	ID             string       `json:"id"`
	Name           string       `json:"name" binding:"required"`
	Description    string       `json:"description"`
	InitialURLs    []ReqURL     `json:"initial_urls" binding:"required,min=1"`
	Priority       TaskPriority `json:"priority" binding:"required,min=1,max=3"`
	SpiderName     string       `json:"spider_name" binding:"required"`
	MaxConcurrency int          `json:"max_concurrency" binding:"min=1,max=100"`
	MaxQPS         int          `json:"max_qps" binding:"min=1,max=1000"`
	Timeout        int          `json:"timeout" binding:"min=1,max=300"` // 请求超时时间，单位：秒
	Status         TaskStatus   `json:"status"`
	IsRecurring    bool         `json:"is_recurring"`               // 是否为周期性任务
	RepeatInterval int          `json:"repeat_interval,omitempty"`  // 重复执行的时间间隔（秒）
	LastExecutedAt *time.Time   `json:"last_executed_at,omitempty"` // 上次执行时间
	DownloadConfig string       `json:"download_config,omitempty"`  // 下载配置（JSON字符串）
	ParseConfig    string       `json:"parse_config"`               // 解析配置（JSON字符串）
	CreatedAt      time.Time    `json:"created_at"`
	UpdatedAt      time.Time    `json:"updated_at"`
}

// CreateTaskRequest 定义创建任务的请求结构
type CreateTaskRequest struct {
	Name           string       `json:"name" binding:"required"`
	Description    string       `json:"description"`
	InitialURLs    []ReqURL     `json:"initial_urls" binding:"required,min=1"`
	Priority       TaskPriority `json:"priority" binding:"required,min=1,max=3"`
	SpiderName     string       `json:"spider_name" binding:"required"`
	MaxConcurrency int          `json:"max_concurrency" binding:"min=1,max=100"`
	MaxQPS         int          `json:"max_qps" binding:"min=1,max=1000"`
	Timeout        int          `json:"timeout" binding:"min=1,max=300"` // 请求超时时间，单位：秒
	IsRecurring    bool         `json:"is_recurring"`                    // 是否为周期性任务
	RepeatInterval int          `json:"repeat_interval,omitempty"`       // 重复执行的时间间隔（秒）
	DownloadConfig string       `json:"download_config,omitempty"`       // 下载配置（JSON字符串）
	ParseConfig    string       `json:"parse_config"`                    // 解析配置（JSON字符串）
}

// URLStatus 定义URL处理状态
type URLStatus string

const (
	URLStatusPending    URLStatus = "pending"
	URLStatusScheduled  URLStatus = "scheduled"
	URLStatusProcessing URLStatus = "processing"
	URLStatusCompleted  URLStatus = "completed"
	URLStatusFailed     URLStatus = "failed"
)

// URLQueueItem 定义URL队列项结构
type URLQueueItem struct {
	ID           int64        `json:"id" db:"id"`
	TaskID       string       `json:"task_id" db:"task_id"`
	TaskName     string       `json:"task_name"` // 任务名称（非数据库字段）
	URL          string       `json:"url" db:"url"`
	Method       HTTPMethod   `json:"method" db:"method"`
	Body         string       `json:"body,omitempty" db:"body"`
	Priority     TaskPriority `json:"priority" db:"priority"`
	PageType     string       `json:"page_type" db:"page_type"`
	Status       URLStatus    `json:"status" db:"status"`
	RetryCount   int          `json:"retry_count" db:"retry_count"`
	ErrorMessage string       `json:"error_message,omitempty" db:"error_message"`
	CreatedAt    time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at" db:"updated_at"`
}

// TaskCreatedEvent 定义任务创建事件结构
type TaskCreatedEvent struct {
	TaskID     string       `json:"task_id"`
	TaskName   string       `json:"task_name"`
	Priority   TaskPriority `json:"priority"`
	SpiderName string       `json:"spider_name"`
	URLCount   int          `json:"url_count"`
	CreatedAt  time.Time    `json:"created_at"`
}

// URLQueueQuery 定义URL队列查询参数
type URLQueueQuery struct {
	TaskID   string       `json:"task_id,omitempty"`
	Status   URLStatus    `json:"status,omitempty"`
	Priority TaskPriority `json:"priority,omitempty"`
	PageType string       `json:"page_type,omitempty"`
	Limit    int          `json:"limit,omitempty"`
	Offset   int          `json:"offset,omitempty"`
}

// ErrorResponse 定义错误响应结构
type ErrorResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Error   string      `json:"error,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

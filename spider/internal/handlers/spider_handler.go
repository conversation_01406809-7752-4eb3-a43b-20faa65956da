package handlers

import (
	"net/http"
	"strconv"
	"time"

	"golden_crawler/internal/models"
	"golden_crawler/internal/services"

	"github.com/gin-gonic/gin"
)

// SpiderHandler 爬虫处理器
type SpiderHandler struct {
	spiderService services.SpiderManagerService
}

// NewSpiderHandler 创建新的爬虫处理器
func NewSpiderHandler(spiderService services.SpiderManagerService) *SpiderHandler {
	return &SpiderHandler{
		spiderService: spiderService,
	}
}

// RegisterSpider 注册爬虫
// @Summary 注册爬虫
// @Description 注册一个新的爬虫
// @Tags spiders
// @Accept json
// @Produce json
// @Param spider body models.CreateSpiderRequest true "爬虫信息"
// @Success 201 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/spiders [post]
func (h *SpiderHandler) RegisterSpider(c *gin.Context) {
	var req models.CreateSpiderRequest

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 调用服务层注册爬虫
	spider, exists, err := h.spiderService.RegisterSpider(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "注册爬虫失败",
			Error:   err.Error(),
		})
		return
	}

	if exists {
		c.JSON(http.StatusOK, models.ErrorResponse{
			Success: true,
			Message: "爬虫已存在",
			Data:    spider,
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusCreated, models.ErrorResponse{
		Success: true,
		Message: "爬虫注册成功",
		Data:    spider,
	})
}

// GetSpider 获取爬虫信息
// @Summary 获取爬虫信息
// @Description 根据爬虫名称获取爬虫详细信息
// @Tags spiders
// @Produce json
// @Param name path string true "爬虫名称"
// @Success 200 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/spiders/{name} [get]
func (h *SpiderHandler) GetSpider(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "无效的爬虫名称",
			Error:   "名称不能为空",
		})
		return
	}

	spider, err := h.spiderService.GetSpider(c.Request.Context(), name)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Success: false,
			Message: "爬虫不存在",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "获取爬虫成功",
		Data:    spider,
	})
}

// ListSpiders 获取爬虫列表
// @Summary 获取爬虫列表
// @Description 获取爬虫列表，支持分页
// @Tags spiders
// @Produce json
// @Param limit query int false "每页数量限制，默认10"
// @Param offset query int false "偏移量，默认0"
// @Success 200 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/spiders [get]
func (h *SpiderHandler) ListSpiders(c *gin.Context) {
	limit := 10
	offset := 0

	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 {
			limit = parsed
		}
	}

	if o := c.Query("offset"); o != "" {
		if parsed, err := strconv.Atoi(o); err == nil && parsed >= 0 {
			offset = parsed
		}
	}

	spiders, total, err := h.spiderService.ListSpiders(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取爬虫列表失败",
			Error:   err.Error(),
		})
		return
	}

	// 构建分页响应
	response := map[string]interface{}{
		"spiders": spiders,
		"pagination": map[string]interface{}{
			"total":  total,
			"limit":  limit,
			"offset": offset,
		},
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "获取爬虫列表成功",
		Data:    response,
	})
}

// UpdateSpider 更新爬虫信息
// @Summary 更新爬虫信息
// @Description 更新爬虫的描述和心跳时间
// @Tags spiders
// @Accept json
// @Produce json
// @Param name path string true "爬虫名称"
// @Param spider body models.UpdateSpiderRequest true "爬虫更新信息"
// @Success 200 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/spiders/{name} [put]
func (h *SpiderHandler) UpdateSpider(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "无效的爬虫名称",
			Error:   "名称不能为空",
		})
		return
	}

	var req models.UpdateSpiderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 如果心跳时间为空，设置为当前时间
	if req.HeartbeatAt.IsZero() {
		req.HeartbeatAt = time.Now()
	}

	err := h.spiderService.UpdateSpider(c.Request.Context(), name, &req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		message := "更新爬虫失败"

		if err.Error() == "爬虫不存在: "+name {
			statusCode = http.StatusNotFound
			message = "爬虫不存在"
		}

		c.JSON(statusCode, models.ErrorResponse{
			Success: false,
			Message: message,
			Error:   err.Error(),
		})
		return
	}

	// 获取更新后的爬虫
	spider, err := h.spiderService.GetSpider(c.Request.Context(), name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取更新后的爬虫失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "更新爬虫成功",
		Data:    spider,
	})
}

// DeleteSpider 删除爬虫
// @Summary 删除爬虫
// @Description 根据爬虫名称删除爬虫
// @Tags spiders
// @Produce json
// @Param name path string true "爬虫名称"
// @Success 200 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/spiders/{name} [delete]
func (h *SpiderHandler) DeleteSpider(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "无效的爬虫名称",
			Error:   "名称不能为空",
		})
		return
	}

	err := h.spiderService.DeleteSpider(c.Request.Context(), name)
	if err != nil {
		statusCode := http.StatusInternalServerError
		message := "删除爬虫失败"

		if err.Error() == "爬虫不存在: "+name {
			statusCode = http.StatusNotFound
			message = "爬虫不存在"
		}

		c.JSON(statusCode, models.ErrorResponse{
			Success: false,
			Message: message,
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "删除爬虫成功",
	})
}

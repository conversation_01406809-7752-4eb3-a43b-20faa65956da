#!/bin/bash

# 任务服务测试脚本
# 用于测试URL调度服务和任务执行服务的基本功能

echo "=== 任务服务测试脚本 ==="

# 检查必要的服务是否运行
echo "1. 检查Dapr服务状态..."
dapr list

echo ""
echo "2. 检查Redis服务状态..."
redis-cli ping 2>/dev/null || echo "警告: Redis服务未运行，请先启动Redis"

echo ""
echo "3. 检查MySQL服务状态..."
mysql -h localhost -u root -p123456 -e "SELECT 1;" 2>/dev/null || echo "警告: MySQL服务未运行或连接失败"

echo ""
echo "4. 测试任务管理服务健康检查..."
curl -s http://localhost:8080/health || echo "警告: 任务管理服务未响应"

echo ""
echo "5. 创建测试任务..."
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试任务",
    "description": "任务服务测试任务",
    "initial_urls": [
      {
        "url": "https://httpbin.org/get",
        "method": "GET",
        "priority": 1
      },
      {
        "url": "https://httpbin.org/post",
        "method": "POST",
        "body": "{\"test\": \"data\"}",
        "priority": 2
      }
    ],
    "priority": 1,
    "downloader_name": "default",
    "parser_name": "default",
    "max_concurrency": 5,
    "max_qps": 10,
    "timeout": 30
  }'

echo ""
echo ""
echo "=== 测试完成 ==="
echo "请观察服务日志以确认URL调度和任务执行流程是否正常工作"
echo ""
echo "启动服务命令:"
echo "dapr run -f examples/url-services-dapr-config.yaml"

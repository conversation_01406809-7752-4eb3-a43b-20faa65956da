#!/bin/bash

# 诊断和修复task-executor服务的问题
# 作者: 系统管理员
# 日期: 2023-10-01

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始诊断task-executor服务问题...${NC}"

# 检查task-executor服务是否运行
echo -e "检查task-executor服务状态..."
TASK_EXECUTOR_RUNNING=$(ps aux | grep -v grep | grep "task_executor" | wc -l)

if [ $TASK_EXECUTOR_RUNNING -eq 0 ]; then
    echo -e "${RED}错误: task-executor服务未运行!${NC}"
    echo -e "尝试启动服务..."
    dapr stop task-executor 2>/dev/null
    sleep 2
    dapr run -f dapr/run-dev.yaml &
    echo -e "${GREEN}已尝试重启服务，请等待服务完全启动...${NC}"
    sleep 5
fi

# 检查端口8082是否在监听
echo -e "检查端口8082是否在监听..."
PORT_LISTENING=$(lsof -i :8082 | wc -l)

if [ $PORT_LISTENING -eq 0 ]; then
    echo -e "${RED}错误: 端口8082未在监听!${NC}"
    echo -e "检查task-executor服务配置..."
    
    # 检查SERVER_PORT环境变量
    echo -e "检查dapr/run-dev.yaml中的SERVER_PORT配置..."
    SERVER_PORT=$(grep "SERVER_PORT" dapr/run-dev.yaml | grep task-executor -A 1 | tail -1 | awk -F'"' '{print $2}')
    
    if [ "$SERVER_PORT" != "8082" ]; then
        echo -e "${RED}错误: SERVER_PORT配置不正确!${NC}"
        echo -e "当前值: $SERVER_PORT, 应为: 8082"
        echo -e "请修改dapr/run-dev.yaml中task-executor服务的SERVER_PORT为8082"
    fi
else
    echo -e "${GREEN}✅ 端口8082正在监听${NC}"
fi

# 检查Dapr sidecar是否正常运行
echo -e "检查Dapr sidecar是否正常运行..."
DAPR_SIDECAR_RUNNING=$(ps aux | grep -v grep | grep "daprd" | grep "task-executor" | wc -l)

if [ $DAPR_SIDECAR_RUNNING -eq 0 ]; then
    echo -e "${RED}错误: task-executor的Dapr sidecar未运行!${NC}"
    echo -e "尝试重启服务..."
    dapr stop task-executor 2>/dev/null
    sleep 2
    dapr run -f dapr/run-dev.yaml &
    echo -e "${GREEN}已尝试重启服务，请等待服务完全启动...${NC}"
    sleep 5
else
    echo -e "${GREEN}✅ Dapr sidecar正在运行${NC}"
fi

# 检查pubsub组件是否正确配置
echo -e "检查pubsub组件配置..."
if [ -f "dapr/components/pubsub.yaml" ]; then
    PUBSUB_SCOPES=$(grep -A 10 "scopes:" dapr/components/pubsub.yaml | grep "task-executor" | wc -l)
    
    if [ $PUBSUB_SCOPES -eq 0 ]; then
        echo -e "${RED}错误: pubsub组件未包含task-executor在scopes中!${NC}"
        echo -e "正在修复pubsub.yaml..."
        
        # 备份原文件
        cp dapr/components/pubsub.yaml dapr/components/pubsub.yaml.bak
        
        # 添加task-executor到scopes
        sed -i '' 's/scopes:/scopes:\n- task-executor/g' dapr/components/pubsub.yaml 2>/dev/null || \
        sed -i 's/scopes:/scopes:\n- task-executor/g' dapr/components/pubsub.yaml
        
        echo -e "${GREEN}已修复pubsub.yaml，添加了task-executor到scopes${NC}"
        echo -e "请重启系统: make dapr-stop-all && make dapr-run-dev"
    else
        echo -e "${GREEN}✅ pubsub组件已正确配置task-executor${NC}"
    fi
else
    echo -e "${RED}错误: 找不到pubsub.yaml配置文件!${NC}"
fi

# 检查Dapr订阅是否正常
echo -e "检查Dapr订阅是否正常..."
SUBSCRIPTION_REGISTERED=$(curl -s http://localhost:3502/v1.0/metadata | grep -q "url-download" && echo "yes" || echo "no")

if [ "$SUBSCRIPTION_REGISTERED" = "no" ]; then
    echo -e "${RED}错误: task-executor未正确订阅url-download主题!${NC}"
    echo -e "请检查internal/services/task_executor_service.go中的订阅配置"
    echo -e "确保订阅路由为'/download-url'且主题名称为'url-download'"
else
    echo -e "${GREEN}✅ task-executor已正确订阅url-download主题${NC}"
fi

# 总结
echo -e "\n${YELLOW}诊断总结:${NC}"
echo -e "1. 如果服务未运行，请使用 'make dapr-run-dev' 启动所有服务"
echo -e "2. 如果端口配置不正确，请修改dapr/run-dev.yaml中的SERVER_PORT"
echo -e "3. 如果pubsub配置不正确，已自动修复，请重启系统"
echo -e "4. 如果订阅未正确注册，请检查代码中的订阅配置"
echo -e "\n${YELLOW}建议的修复步骤:${NC}"
echo -e "1. 确保pubsub.yaml中包含task-executor在scopes列表中"
echo -e "2. 确保task-executor服务在端口8082上运行"
echo -e "3. 重启整个系统: make dapr-stop-all && make dapr-run-dev"
echo -e "4. 使用 'make test-task-executor' 验证服务是否正常" 
-- 爬虫任务管理服务 MySQL 初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS crawler_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE crawler_db;

-- 创建Dapr状态存储表
-- Dapr会自动创建这个表，但我们可以预先创建以确保结构正确
CREATE TABLE IF NOT EXISTS state (
    id VARCHAR(255) NOT NULL PRIMARY KEY,
    value LONGTEXT,
    isbinary BOOLEAN NOT NULL DEFAULT FALSE,
    insertdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedate TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    eTag VARCHAR(36)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_state_updatedate ON state(updatedate);
CREATE INDEX IF NOT EXISTS idx_state_insertdate ON state(insertdate);

-- 创建用户（可选，用于生产环境）
-- CREATE USER IF NOT EXISTS 'crawler_user'@'%' IDENTIFIED BY 'crawler_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON crawler_db.* TO 'crawler_user'@'%';
-- FLUSH PRIVILEGES;

-- 创建URL队列表
CREATE TABLE IF NOT EXISTS url_queue (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(255) NOT NULL COMMENT '关联的任务ID',
    url VARCHAR(2048) NOT NULL COMMENT '待下载的URL',
    method ENUM('GET', 'POST') NOT NULL COMMENT 'HTTP请求方法',
    body TEXT COMMENT 'POST请求体，仅当method为POST时使用',
    priority INT NOT NULL COMMENT '优先级，1=低，2=中，3=高',
    page_type VARCHAR(100) NOT NULL DEFAULT '' COMMENT '页面类型，用于区分不同类型的页面',
    status ENUM('pending', 'scheduled', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT 'URL处理状态',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX idx_task_id (task_id),
    INDEX idx_status_priority (status, priority DESC),
    INDEX idx_created_at (created_at),
    INDEX idx_status_retry (status, retry_count),
    INDEX idx_page_type (page_type)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='URL下载队列表';

-- 显示表结构
DESCRIBE state;
DESCRIBE crawler_tasks;
DESCRIBE url_queue;

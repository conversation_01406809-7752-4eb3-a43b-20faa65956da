#!/bin/bash

# 此脚本用于设置MongoDB配置到Redis配置存储
# 注意：需要安装Redis和Redis CLI

# 检查Redis CLI是否安装
if ! command -v redis-cli &> /dev/null; then
  echo "错误: redis-cli未安装，请先安装Redis"
  echo "可以使用以下命令安装Redis:"
  echo "  macOS: brew install redis"
  echo "  Ubuntu: apt-get install redis"
  echo "  CentOS: yum install redis"
  exit 1
fi

# 检查Redis服务是否运行
if ! redis-cli ping &> /dev/null; then
  echo "错误: Redis服务未运行，请先启动Redis服务"
  echo "可以使用以下命令启动Redis服务:"
  echo "  macOS: brew services start redis"
  echo "  Linux: systemctl start redis"
  exit 1
fi

echo "设置MongoDB配置到Redis..."

# 设置MongoDB URI
redis-cli SET mongo_uri "mongodb://localhost:27017"
if [ $? -ne 0 ]; then
  echo "设置MongoDB URI失败"
  exit 1
fi

# 设置MongoDB数据库名称
redis-cli SET mongo_database "crawler_data"
if [ $? -ne 0 ]; then
  echo "设置MongoDB数据库名称失败"
  exit 1
fi

echo "MongoDB配置已成功设置到Redis"
echo "配置项:"
echo "  - mongo_uri: mongodb://localhost:27017"
echo "  - mongo_database: crawler_data"

echo "提示: 确保Dapr配置存储组件(configstore)已正确配置为使用Redis" 
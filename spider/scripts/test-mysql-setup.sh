#!/bin/bash

# MySQL状态存储测试脚本

set -e

echo "🚀 开始测试MySQL状态存储配置..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装"
    exit 1
fi

echo "✅ Docker环境检查通过"

# 启动MySQL
echo "📦 启动MySQL容器..."
docker-compose -f docker-compose-mysql.yml up -d

# 等待MySQL启动
echo "⏳ 等待MySQL启动..."
sleep 10

# 检查MySQL是否运行
if ! docker ps | grep mysql-crawler > /dev/null; then
    echo "❌ MySQL容器启动失败"
    docker-compose -f docker-compose-mysql.yml logs mysql
    exit 1
fi

echo "✅ MySQL容器启动成功"

# 测试MySQL连接
echo "🔗 测试MySQL连接..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker exec mysql-crawler mysqladmin ping -h localhost --silent; then
        echo "✅ MySQL连接成功"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo "❌ MySQL连接失败，超过最大重试次数"
        docker-compose -f docker-compose-mysql.yml logs mysql
        exit 1
    fi
    
    echo "⏳ 等待MySQL就绪... (尝试 $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# 验证数据库和表
echo "🔍 验证数据库结构..."
docker exec mysql-crawler mysql -u root -ppassword -e "
USE crawler_db;
SHOW TABLES;
DESCRIBE state;
SELECT 'Database setup verified' as status;
"

echo "✅ 数据库结构验证成功"

# 切换到MySQL配置
echo "🔄 切换到MySQL状态存储配置..."
cp dapr/components/statestore-mysql.yaml dapr/components/statestore.yaml
echo "✅ 已切换到MySQL配置"

# 检查Dapr CLI
if ! command -v dapr &> /dev/null; then
    echo "⚠️  Dapr CLI未安装，跳过Dapr测试"
    echo "📋 手动测试步骤："
    echo "   1. 安装Dapr CLI: https://docs.dapr.io/getting-started/install-dapr-cli/"
    echo "   2. 运行: make dapr-run-dev"
    echo "   3. 测试API: make test-api"
else
    echo "✅ Dapr CLI已安装"
    echo "🎯 可以运行以下命令测试完整功能："
    echo "   make dapr-run-dev"
    echo "   make test-api"
fi

echo ""
echo "🎉 MySQL状态存储配置测试完成！"
echo ""
echo "📋 下一步："
echo "   1. 启动服务: make dapr-run-dev"
echo "   2. 测试API: make test-api"
echo "   3. 查看数据: make mysql-connect"
echo "   4. 查看日志: make mysql-logs"
echo ""
echo "🔧 管理命令："
echo "   - 停止MySQL: make mysql-stop"
echo "   - 重启MySQL: make mysql-restart"
echo "   - 切换到Redis: make switch-to-redis"
echo ""

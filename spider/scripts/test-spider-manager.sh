#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 设置服务地址
API_URL="http://localhost:8084/api/v1/spiders"

echo -e "${YELLOW}测试爬虫管理服务API...${NC}"

# 测试注册爬虫
echo -e "\n${GREEN}1. 注册爬虫:${NC}"
REGISTER_RESPONSE=$(curl -s -X POST $API_URL \
    -H "Content-Type: application/json" \
    -d '{
        "name": "test-spider",
        "description": "测试爬虫"
    }')
echo $REGISTER_RESPONSE | jq .

# 从响应中提取爬虫ID
SPIDER_ID=$(echo $REGISTER_RESPONSE | jq -r '.data.id')
echo -e "${GREEN}爬虫ID: $SPIDER_ID${NC}"

# 测试获取爬虫列表
echo -e "\n${GREEN}2. 获取爬虫列表:${NC}"
curl -s $API_URL | jq .

# 测试获取爬虫详情
echo -e "\n${GREEN}3. 获取爬虫详情:${NC}"
curl -s $API_URL/$SPIDER_ID | jq .

# 测试更新爬虫
echo -e "\n${GREEN}4. 更新爬虫:${NC}"
curl -s -X PUT $API_URL/$SPIDER_ID \
    -H "Content-Type: application/json" \
    -d '{
        "description": "更新的测试爬虫描述"
    }' | jq .

# 再次获取爬虫详情，验证更新
echo -e "\n${GREEN}5. 验证更新后的爬虫:${NC}"
curl -s $API_URL/$SPIDER_ID | jq .

# 测试更新心跳时间
echo -e "\n${GREEN}6. 更新爬虫心跳时间:${NC}"
curl -s -X PUT $API_URL/$SPIDER_ID \
    -H "Content-Type: application/json" \
    -d '{
        "heartbeat_at": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"
    }' | jq .

# 测试删除爬虫
echo -e "\n${GREEN}7. 删除爬虫:${NC}"
curl -s -X DELETE $API_URL/$SPIDER_ID | jq .

# 验证爬虫已被删除
echo -e "\n${GREEN}8. 验证爬虫已被删除:${NC}"
curl -s $API_URL/$SPIDER_ID | jq .

echo -e "\n${GREEN}测试完成!${NC}" 
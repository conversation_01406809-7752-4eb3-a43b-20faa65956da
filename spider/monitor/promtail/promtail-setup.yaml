server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # 收集本地托管模式下的Dapr应用日志
  - job_name: dapr_apps_logs
    static_configs:
    - targets:
        - localhost
      labels:
        job: dapr_apps
        app: task-manager
        __path__: /app/spider/.dapr/logs/task-manager_app_*.log
    - targets:
        - localhost
      labels:
        job: dapr_apps
        app: url-scheduler
        __path__: /app/spider/.dapr/logs/url-scheduler_app_*.log
    - targets:
        - localhost
      labels:
        job: dapr_apps
        app: task-executor
        __path__: /app/spider/.dapr/logs/task-executor_app_*.log
    - targets:
        - localhost
      labels:
        job: dapr_apps
        app: spider
        __path__: /app/spider/.dapr/logs/spider_app_*.log
    - targets:
        - localhost
      labels:
        job: dapr_apps
        app: spider-manager
        __path__: /app/spider/.dapr/logs/spider-manager_app_*.log
    - targets:
        - localhost
      labels:
        job: dapr_apps
        app: JSON_API-Spider
        __path__: /app/spider/.dapr/logs/JSON_API-Spider_app_*.log
    - targets:
        - localhost
      labels:
        job: dapr_apps
        app: File-Spider
        __path__: /app/spider/.dapr/logs/File-Spider_app_*.log

  # 收集Dapr sidecar日志
  - job_name: dapr_sidecar_logs
    static_configs:
    - targets:
        - localhost
      labels:
        job: dapr_sidecar
        app: task-manager
        __path__: /app/spider/.dapr/logs/task-manager_daprd_*.log
    - targets:
        - localhost
      labels:
        job: dapr_sidecar
        app: url-scheduler
        __path__: /app/spider/.dapr/logs/url-scheduler_daprd_*.log
    - targets:
        - localhost
      labels:
        job: dapr_sidecar
        app: task-executor
        __path__: /app/spider/.dapr/logs/task-executor_daprd_*.log
    - targets:
        - localhost
      labels:
        job: dapr_sidecar
        app: spider
        __path__: /app/spider/.dapr/logs/spider_daprd_*.log
    - targets:
        - localhost
      labels:
        job: dapr_sidecar
        app: spider-manager
        __path__: /app/spider/.dapr/logs/spider-manager_daprd_*.log
    - targets:
        - localhost
      labels:
        job: dapr_sidecar
        app: JSON_API-Spider
        __path__: /app/spider/.dapr/logs/JSON_API-Spider_daprd_*.log
    - targets:
        - localhost
      labels:
        job: dapr_sidecar
        app: File-Spider
        __path__: /app/spider/.dapr/logs/File-Spider_daprd_*.log

  # 收集系统日志
  - job_name: system
    static_configs:
    - targets:
        - localhost
      labels:
        job: varlogs
        __path__: /var/log/*.log 
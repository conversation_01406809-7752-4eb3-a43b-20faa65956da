apiVersion: 1

providers:
  - name: 'Dapr'
    orgId: 1
    folder: 'Dapr'
    type: file
    disableDeletion: false
    editable: true
    options:
      path: /etc/grafana/provisioning/dashboards/json/dapr-overview.json

  - name: 'Crawler'
    orgId: 1
    folder: 'Crawler'
    type: file
    disableDeletion: false
    editable: true
    options:
      path: /etc/grafana/provisioning/dashboards/json/crawler-services-dashboard.json

  - name: 'Components'
    orgId: 1
    folder: 'Components'
    type: file
    disableDeletion: false
    editable: true
    options:
      path: /etc/grafana/provisioning/dashboards/json/dapr-components-dashboard.json

  - name: 'System'
    orgId: 1
    folder: 'System'
    type: file
    disableDeletion: false
    editable: true
    options:
      path: /etc/grafana/provisioning/dashboards/json/system-metrics-dashboard.json

  - name: 'Logs'
    orgId: 1
    folder: 'Logs'
    type: file
    disableDeletion: false
    editable: true
    options:
      path: /etc/grafana/provisioning/dashboards/json/dapr-logs-dashboard.json 
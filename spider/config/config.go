package config

import (
	"os"
	"strconv"
)

// Config 应用配置结构
type Config struct {
	Server ServerConfig `json:"server"`
	Dapr   DaprConfig   `json:"dapr"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `json:"port"`
	Host string `json:"host"`
}

// DaprConfig Dapr配置
type DaprConfig struct {
	AppID       string `json:"app_id"`
	AppPort     string `json:"app_port"`
	StateStore  string `json:"state_store"`
	PubSubName  string `json:"pubsub_name"`
	SecretStore string `json:"secret_store"`
}

// LoadConfig 加载配置
func LoadConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "8080"),
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
		},
		Dapr: DaprConfig{
			AppID:       getEnv("DAPR_APP_ID", "task-manager"),
			AppPort:     getEnv("DAPR_APP_PORT", "8080"),
			StateStore:  getEnv("DAPR_STATE_STORE", "statestore"),
			PubSubName:  getEnv("DAPR_PUBSUB_NAME", "pubsub"),
			SecretStore: getEnv("DAPR_SECRET_STORE", "secretstore"),
		},
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsBool 获取环境变量并转换为布尔值
func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

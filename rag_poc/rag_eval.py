import json
from ragas.metrics import answer_relevancy
from ragas import evaluate
from ragas.llms import LangchainLLMWrapper
from ragas import EvaluationDataset
from ragas.metrics import LLMContextRecall
from rag_agent import create_rag_agent, user_query
from config import OLLAMA_BASE_URL, OLLAMA_MODEL, CHROMA_PERSIST_DIR, CHROMA_HOST
from chromadb import Http<PERSON>lient
from langchain_chroma import Chroma
from langchain_ollama import OllamaEmbeddings
from langchain_ollama.llms import OllamaLLM
from ragas.run_config import RunConfig

# Load data from eval.json
with open('/Users/<USER>/Documents/pyspace/shovel/eval.json', 'r') as f:
    data = json.load(f)

# Prepare data for evaluation
questions = [item['question'] for item in data]
answers = [item['answer'] for item in data]

# Assume agent_answers are the answers generated by the agent
vector_db = Chroma(persist_directory=CHROMA_PERSIST_DIR, embedding_function=OllamaEmbeddings(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL), client=HttpClient(host=CHROMA_HOST))
agent = create_rag_agent(vector_db)
dataset = []

for item in data:
    relevant_doc = vector_db.similarity_search(item['question'],k=1)[0].page_content
    dataset.append(
        {
            "user_input":item['question'],
            "retrieved_contexts":[relevant_doc],
            "response":user_query(agent, item['question'])["result"] ,
            "reference":item['answer']
        }
    )
# dataset.append(
#     {
#         "user_input":"When was the first super bowl?",
#         "retrieved_contexts":["The First AFL–NFL World Championship Game was an American football game played on January 15, 1967, at the Los Angeles Memorial Coliseum in Los Angeles."],
#         "response":"The first superbowl was held on Jan 15, 1967",
#         "reference":"A bank is a financial institution that provides financial services to individuals and businesses."
#     }
# )
evaluation_dataset = EvaluationDataset.from_list(dataset)
print(dataset)
evaluator_llm = LangchainLLMWrapper(OllamaLLM(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL))

# Evaluate
results = evaluate(
    dataset=evaluation_dataset,
    llm=evaluator_llm,
    run_config=RunConfig(timeout=600, max_wait=300),
    embeddings=OllamaEmbeddings(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL),
    raise_exceptions=False,
    metrics=[answer_relevancy]
)

print(results)
# Create server parameters for stdio connection
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import asyncio

from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
from config import OLLAMA_BASE_URL, OLLAMA_MODEL
from langchain_ollama import ChatOllama

async def main():
    server_params = StdioServerParameters(
        command="python",
        # Make sure to update to the full absolute path to your math_server.py file
        args=["./rag_poc/aave_mcp_server.py"],
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the connection
            await session.initialize()

            # Get tools
            tools = await load_mcp_tools(session)
            print(tools)

            # Create and run the agent
            agent = create_react_agent(ChatOllama(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL), tools)
            # Print the graph
            #agent.get_graph().draw_png(output_file_path="graph.png")
            # from IPython.display import Image, display
            # try:
            #     display(Image(agent.get_graph().draw_mermaid_png()))
            # except Exception:
            #     # This requires some extra dependencies and is optional
            #     pass
            agent_response = await agent.ainvoke({"messages": "query balance for 0x475b3f9dbaaaebcbfaeacdfedcbecdeee at aave pool 0xBcca60bb6193408095F964433EEDC2B"})
            print("Agent response: ", agent_response)

if __name__ == "__main__":
    asyncio.run(main())
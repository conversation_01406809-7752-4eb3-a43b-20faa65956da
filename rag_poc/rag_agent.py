from langchain.agents import AgentType
from langchain.chains import create_retrieval_chain
from langchain_ollama import OllamaEmbeddings
from langchain_ollama.llms import OllamaLLM
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_chroma import Chroma
from langchain.document_loaders import PyPDFLoader, WebBaseLoader
from config import OLLAMA_BASE_URL, OLLAMA_MODEL, CHROMA_PERSIST_DIR, CHROMA_HOST, SPLITTER_CHUNK_SIZE, SPLITTER_CHUNK_OVERLAP
from chromadb import HttpClient
from langchain import hub
from langchain.chains.combine_documents import create_stuff_documents_chain
import os


def create_vector_db_instance(texts=None):
    print('开始创建向量数据库实例')
    embeddings = OllamaEmbeddings(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL)
    if texts:
        print('根据文档创建向量数据库')
        vector_db = Chroma.from_documents(texts, embeddings, persist_directory=CHROMA_PERSIST_DIR, client=HttpClient(host=CHROMA_HOST))
        
    else:
        print('从持久化目录加载向量数据库')
        vector_db = Chroma(persist_directory=CHROMA_PERSIST_DIR, embedding_function=embeddings, client=HttpClient(host=CHROMA_HOST))
    print('向量数据库实例创建完成')
    return vector_db


def load_pdfs(docs_dir=None):
    print('开始加载 PDF 文档')
    if docs_dir is None:
        print('未提供文档目录，返回空列表')
        return []
    documents = []
    for file in os.listdir(docs_dir):
        if file.endswith('.pdf'):
            print(f'正在加载文件: {file}')
            loader = PyPDFLoader(os.path.join(docs_dir, file), mode="single")
            documents.extend(loader.load())
    print('PDF 文档加载完成')
    return documents


def load_web_pages(urls=None):
    print('开始加载网页文档')
    if urls is None:
        print('未提供网页 URL，返回空列表')
        return []
    documents = []
    for url in urls:
        print(f'正在加载网页: {url}')
        loader = WebBaseLoader(url)
        documents.extend(loader.load())
    print('网页文档加载完成')
    return documents


def process_documents(documents):
    print('开始处理文档')
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=SPLITTER_CHUNK_SIZE, chunk_overlap=SPLITTER_CHUNK_OVERLAP)
    texts = text_splitter.split_documents(documents)
    print('文档处理完成')
    return texts


def create_vector_db(texts):
    print('开始创建向量数据库')
    vector_db = create_vector_db_instance(texts)
    
    print('向量数据库创建完成')
    return vector_db


def check_vector_db_exists():
    print('开始检查向量数据库是否存在')
    import os
    if os.path.isdir(CHROMA_PERSIST_DIR) and os.listdir(CHROMA_PERSIST_DIR):
        print('向量数据库存在')
        return True
    print('向量数据库不存在')
    return False


def add_single_document(file_path=None, url=None):
    print('开始添加单个文档')
    if file_path:
        print(f'正在加载文件: {file_path}')
        loader = PyPDFLoader(file_path)
        documents = loader.load()
    elif url:
        print(f'正在加载网页: {url}')
        loader = WebBaseLoader(url)
        documents = loader.load()
    else:
        print('未提供文件路径或网页 URL，返回 None')
        return None
    texts = process_documents(documents)
    vector_db = create_vector_db_instance(None)
    vector_db.add_documents(texts)
    
    print('单个文档添加完成')
    return vector_db


def add_batch_documents(file_paths=None, urls=None):
    print('开始添加批量文档')
    all_documents = []
    if file_paths:
        for path in file_paths:
            print(f'正在加载文件: {path}')
            loader = PyPDFLoader(path)
            all_documents.extend(loader.load())
    if urls:
        for url in urls:
            print(f'正在加载网页: {url}')
            loader = WebBaseLoader(url)
            all_documents.extend(loader.load())
    texts = process_documents(all_documents)
    vector_db = create_vector_db_instance(None)
    vector_db.add_documents(texts)
    
    print('批量文档添加完成')
    return vector_db


def delete_single_document(document_id):
    print('开始删除单个文档')
    vector_db = create_vector_db_instance(None)
    vector_db.delete([document_id])
    
    print('单个文档删除完成')
    return vector_db


def delete_batch_documents(document_ids):
    print('开始删除批量文档')
    vector_db = create_vector_db_instance(None)
    vector_db.delete(document_ids)
    
    print('批量文档删除完成')
    return vector_db

def create_rag_agent(vector_db):
    print('开始创建 RAG 代理')
    llm = OllamaLLM(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL)
    # See full prompt at `https://smith.langchain.com/hub/langchain-ai/retrieval-qa-chat`
    retrieval_qa_chat_prompt = hub.pull("langchain-ai/retrieval-qa-chat")
    combine_docs_chain = create_stuff_documents_chain(llm, retrieval_qa_chat_prompt)
    rag_chain = create_retrieval_chain(vector_db.as_retriever(search_kwargs={'k': 1}), combine_docs_chain)
    print('RAG 代理创建完成')
    return rag_chain


def maintain_vector_db(docs_dir, web_urls):
    print('开始维护向量数据库')
    pdf_docs = load_pdfs(docs_dir)
    web_docs = load_web_pages(web_urls)
    all_docs = pdf_docs + web_docs
    processed_texts = process_documents(all_docs)
    vector_db = create_vector_db(processed_texts)
    print('向量数据库维护完成')
    return vector_db


def user_query(agent, question):
    print('开始处理用户查询')
    result = agent.invoke(question)
    print('用户查询处理完成')
    return result


if __name__ == '__main__':
    # 运维人员增量维护向量数据库
    vector_db = maintain_vector_db(None, None)
    agent = create_rag_agent(vector_db)
    # 普通用户提问
    question = 'What are the key architectural changes in Aave V2 compared to V1?'
    answer = agent.invoke({"input": question})
    print(answer)
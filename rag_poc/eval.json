[{"question": "What are the key architectural changes in Aave V2 compared to V1?", "answer": "Aave V2 introduces a simplified architecture where funds are stored in aTokens contracts instead of LendingPoolCore. The LendingPoolCore and LendingPoolDataProvider contracts have been replaced with libraries, reducing gas costs and improving code efficiency. All operations are now routed through the LendingPool contract."}, {"question": "How does debt tokenization work in Aave V2?", "answer": "Debt tokenization in V2 represents borrower positions through debt tokens (stableDebtToken and variableDebtToken) rather than internal accounting. This enables simultaneous borrowing at different rates and supports native credit delegation through standardized ERC-20 interfaces."}, {"question": "What improvements does Flash Loans V2 introduce?", "answer": "Flash Loans V2 enables combining flash loans with other protocol operations, supporting new use cases like collateral swaps, debt refinancing, and leveraged positions. It implements a pull-based repayment model to prevent reentrancy attacks."}, {"question": "Explain the collateral swap mechanism in V2", "answer": "Collateral swaps allow users to exchange deposited collateral assets without debt repayment. This is achieved through flash loans that temporarily borrow the new collateral asset, execute the swap via an external DEX, and repay the flash loan in a single atomic transaction."}, {"question": "How does credit delegation function in V2?", "answer": "Credit delegation permits approved addresses to borrow against a delegator's credit line without collateral. This is implemented through approveDelegation() in debt tokens, though delegated borrowers cannot mix stable and variable rate debt in single operations."}, {"question": "What gas optimization techniques are employed in V2?", "answer": "V2 implements: 1) bitmasking for reserve/user configuration storage 2) Removal of SafeMath in math libraries 3) Approximate compound interest calculations 4) Optimized storage access patterns - reducing SLOAD operations by 50% in core functions."}, {"question": "How are interest rates calculated differently in V2?", "answer": "V2 introduces scaled balances for both assets (aTokens) and debts. Interest accrual uses cumulative indexes (RAY precision) and scaled balances (WAD precision), with separate calculations for stable vs variable rate products to optimize gas efficiency."}, {"question": "What security measures prevent reentrancy in V2?", "answer": "V2 employs: 1) Checks-effects-interactions pattern 2) Flash loan pull repayments 3) State mutex locks during critical operations 4) Formal verification of core mathematical operations 5) Automated invariant testing for all state transitions."}, {"question": "How does V2 handle reserve configuration updates?", "answer": "Reserve parameters (LTV, liquidation threshold, etc.) are stored as bitmasked values in a single storage slot per asset. Updates occur through privileged functions in the LendingPoolConfigurator contract, with changes taking effect after a time-lock period for major parameters."}]
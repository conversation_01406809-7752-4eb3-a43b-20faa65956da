from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from config import OLLAMA_BASE_URL, OLLAMA_MODEL
from langchain_ollama import ChatOllama
import asyncio
async def main():
    client = MultiServerMCPClient(
        {
            "AaveMCPServer": {
                # make sure you start your weather server on port 8000
                "url": "http://localhost:8000/mcp",
                "transport": "streamable_http",
            }
        }
    )
    tools = await client.get_tools()
    print(tools)
    agent = create_react_agent(ChatOllama(base_url=OLLAMA_BASE_URL, model=OLLAMA_MODEL), tools)
    # agent.get_graph().draw_mermaid_png()
    math_response = await agent.ainvoke({"messages": "query balance for 0x475b3f9dbaaaebcbfaeacdfedcbecdeee at aave pool 0xBcca60bb6193408095F964433EEDC2B"})
    print(math_response)

if __name__ == "__main__":
    asyncio.run(main())
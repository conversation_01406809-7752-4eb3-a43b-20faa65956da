# RAG系统重排序功能说明

## 概述

本RAG系统新增了重排序功能，可以在向量检索后对文档进行二次排序，提高传递给LLM的文档质量，从而提升回答准确性。

## 功能特点

### 支持的重排序算法

1. **相似度重排序 (similarity)**
   - 基于查询词与文档内容的词汇重叠度
   - 计算Jaccard相似度和词汇重叠比例
   - 速度快，无需额外模型
   - 适合快速部署和测试

2. **交叉编码器重排序 (cross_encoder)**
   - 使用预训练的交叉编码器模型
   - 默认模型：`cross-encoder/ms-marco-MiniLM-L-6-v2`
   - 更精确的语义相关性计算
   - 需要安装sentence-transformers

3. **LLM重排序 (llm)**
   - 使用大语言模型判断文档相关性
   - 最智能但计算成本较高
   - 适合对质量要求极高的场景
   - 自动限制文档数量避免过长输入

4. **混合重排序 (hybrid)**
   - 结合多种重排序方法
   - 加权平均多个算法的结果
   - 平衡速度和准确性

## 配置选项

在 `config.py` 中添加了以下配置项：

```python
# 重排序配置
RERANK_ENABLED = True  # 启用/禁用重排序
RERANK_TYPE = "similarity"  # 重排序算法类型
RERANK_TOP_K = 3  # 重排序后返回的文档数量
RERANK_MODEL_NAME = "cross-encoder/ms-marco-MiniLM-L-6-v2"  # 交叉编码器模型
RERANK_MAX_DOCS_FOR_LLM = 10  # LLM重排序的最大文档数
```

### 环境变量配置

```bash
export RERANK_ENABLED=True
export RERANK_TYPE=similarity
export RERANK_TOP_K=3
export RERANK_MODEL_NAME=cross-encoder/ms-marco-MiniLM-L-6-v2
export RERANK_MAX_DOCS_FOR_LLM=10
```

## 安装依赖

### 基础重排序（相似度）
无需额外依赖，使用系统自带的scikit-learn。

### 交叉编码器重排序
```bash
pip install sentence-transformers torch transformers
```

### 完整依赖
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启用重排序
在 `config.py` 中设置：
```python
RERANK_ENABLED = True
RERANK_TYPE = "similarity"  # 或 "cross_encoder", "llm", "hybrid"
```

### 2. API调用
重排序功能会自动集成到现有的查询接口中：

```python
# POST /query
{
    "question": "什么是机器学习？"
}
```

### 3. 程序化使用
```python
from rag_agent import user_query_with_rerank, create_rag_agent, create_vector_db_instance

# 创建向量数据库和RAG代理
vector_db = create_vector_db_instance(None)
agent = create_rag_agent(vector_db)

# 使用重排序查询
result = user_query_with_rerank(agent, "什么是DeFi？", vector_db)
print(result["output"])
```

## 性能对比

### 检索流程对比

**标准检索流程：**
1. 查询 → 向量嵌入
2. 向量相似度搜索 (k=5)
3. 直接传递给LLM

**重排序检索流程：**
1. 查询 → 向量嵌入
2. 向量相似度搜索 (k=10)
3. 重排序算法筛选 (top_k=3)
4. 传递给LLM

### 效果提升

- **相关性提升**：重排序后的文档与查询更相关
- **回答质量**：LLM基于更相关的文档生成更准确的回答
- **计算效率**：虽然增加了重排序步骤，但减少了传递给LLM的文档数量

## 测试验证

### 运行测试
```bash
cd rag_srv
python3 simple_rerank_test.py
```

### 测试内容
1. 重排序器创建测试
2. 重排序功能测试
3. 不同算法对比测试
4. 配置选项测试

### 测试结果示例
```
查询: 什么是机器学习？
重排序结果:
1. 深度学习是机器学习的一个子领域，使用神经网络进行学习。
2. Python是一种高级编程语言，广泛用于数据科学和机器学习。
3. 机器学习是人工智能的一个分支，使用算法从数据中学习模式。
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'sentence_transformers'**
   - 解决：安装依赖 `pip install sentence-transformers`
   - 或使用相似度重排序：`RERANK_TYPE=similarity`

2. **重排序效果不明显**
   - 检查 `RERANK_TOP_K` 设置
   - 尝试不同的重排序算法
   - 确保向量数据库有足够的文档

3. **性能问题**
   - 交叉编码器：考虑使用更小的模型
   - LLM重排序：减少 `RERANK_MAX_DOCS_FOR_LLM`
   - 使用相似度重排序提高速度

### 调试模式

启用调试模式查看详细信息：
```python
DEBUG_MODE = True
```

## 最佳实践

1. **算法选择**
   - 快速部署：使用 `similarity`
   - 平衡效果：使用 `cross_encoder`
   - 最高质量：使用 `llm` 或 `hybrid`

2. **参数调优**
   - 初始检索文档数：8-15个
   - 重排序返回数：3-5个
   - 根据具体场景调整

3. **监控指标**
   - 查询响应时间
   - 重排序成功率
   - 用户满意度

## 扩展开发

### 添加新的重排序算法

1. 继承 `BaseReranker` 类
2. 实现 `rerank` 方法
3. 在 `create_reranker` 函数中注册

```python
class CustomReranker(BaseReranker):
    def rerank(self, query, documents, top_k=None):
        # 自定义重排序逻辑
        return reranked_documents
```

### 集成外部服务

可以集成外部重排序服务，如：
- Cohere Rerank API
- OpenAI Embeddings
- 自定义重排序模型

## 版本历史

- v1.0: 基础重排序功能
  - 相似度重排序
  - 交叉编码器重排序
  - LLM重排序
  - 混合重排序
  - 配置管理
  - 测试验证

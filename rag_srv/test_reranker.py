#!/usr/bin/env python
"""
重排序功能测试脚本
"""

import os
import sys
import time
from typing import List, Dict

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import (
    RERANK_ENABLED, RERANK_TYPE, RERANK_TOP_K, 
    RERANK_MODEL_NAME, RERANK_MAX_DOCS_FOR_LLM
)
from rag_agent import (
    create_rag_agent, check_vector_db_exists, create_vector_db_instance,
    user_query, user_query_with_rerank
)
from reranker import create_reranker


def test_reranker_creation():
    """测试重排序器创建"""
    print("=== 测试重排序器创建 ===")
    
    # 测试相似度重排序器
    try:
        similarity_reranker = create_reranker("similarity")
        print("✓ 相似度重排序器创建成功")
    except Exception as e:
        print(f"✗ 相似度重排序器创建失败: {str(e)}")
    
    # 测试交叉编码器重排序器
    try:
        cross_encoder_reranker = create_reranker("cross_encoder", model_name="cross-encoder/ms-marco-MiniLM-L-6-v2")
        print("✓ 交叉编码器重排序器创建成功")
    except Exception as e:
        print(f"✗ 交叉编码器重排序器创建失败: {str(e)}")
    
    print()


def test_reranker_functionality():
    """测试重排序器功能"""
    print("=== 测试重排序器功能 ===")
    
    # 创建测试文档
    from langchain.schema import Document
    
    test_docs = [
        Document(
            page_content="Python是一种高级编程语言，广泛用于数据科学和机器学习。",
            metadata={"source": "doc1", "similarity_score": 0.7}
        ),
        Document(
            page_content="JavaScript是一种用于网页开发的编程语言。",
            metadata={"source": "doc2", "similarity_score": 0.5}
        ),
        Document(
            page_content="机器学习是人工智能的一个分支，使用算法从数据中学习模式。",
            metadata={"source": "doc3", "similarity_score": 0.8}
        ),
        Document(
            page_content="数据科学结合了统计学、编程和领域知识来从数据中提取洞察。",
            metadata={"source": "doc4", "similarity_score": 0.6}
        )
    ]
    
    query = "什么是机器学习？"
    
    # 测试相似度重排序
    try:
        similarity_reranker = create_reranker("similarity")
        reranked_docs = similarity_reranker.rerank(query, test_docs, top_k=3)
        print(f"✓ 相似度重排序成功，返回{len(reranked_docs)}个文档")
        for i, doc in enumerate(reranked_docs):
            print(f"  {i+1}. {doc.page_content[:50]}...")
    except Exception as e:
        print(f"✗ 相似度重排序失败: {str(e)}")
    
    print()


def test_rag_with_reranking():
    """测试RAG系统的重排序功能"""
    print("=== 测试RAG系统重排序功能 ===")
    
    # 检查向量数据库
    if not check_vector_db_exists():
        print("✗ 向量数据库不存在，请先运行rag_agent.py创建数据库")
        return
    
    # 创建向量数据库实例
    try:
        vector_db = create_vector_db_instance(None)
        print("✓ 向量数据库加载成功")
    except Exception as e:
        print(f"✗ 向量数据库加载失败: {str(e)}")
        return
    
    # 创建RAG代理
    try:
        agent = create_rag_agent(vector_db, debug_mode=False)
        print("✓ RAG代理创建成功")
    except Exception as e:
        print(f"✗ RAG代理创建失败: {str(e)}")
        return
    
    # 测试查询
    test_questions = [
        "什么是DeFi？",
        "Uniswap V3的主要特点是什么？",
        "流动性挖矿是如何工作的？"
    ]
    
    for question in test_questions:
        print(f"\n问题: {question}")
        
        # 标准查询
        try:
            start_time = time.time()
            standard_result = user_query(agent, question, debug_mode=False)
            standard_time = time.time() - start_time
            print(f"✓ 标准查询完成 (用时: {standard_time:.2f}秒)")
            print(f"  回答长度: {len(standard_result['output'])}字符")
        except Exception as e:
            print(f"✗ 标准查询失败: {str(e)}")
            continue
        
        # 重排序查询
        if RERANK_ENABLED:
            try:
                start_time = time.time()
                rerank_result = user_query_with_rerank(agent, question, vector_db, debug_mode=False)
                rerank_time = time.time() - start_time
                print(f"✓ 重排序查询完成 (用时: {rerank_time:.2f}秒)")
                print(f"  回答长度: {len(rerank_result['output'])}字符")
                
                # 比较结果
                if len(rerank_result.get('context', [])) > 0:
                    print(f"  重排序后文档数量: {len(rerank_result['context'])}")
                
            except Exception as e:
                print(f"✗ 重排序查询失败: {str(e)}")
        else:
            print("  重排序功能未启用")
    
    print()


def test_different_reranker_types():
    """测试不同类型的重排序器"""
    print("=== 测试不同重排序器类型 ===")
    
    if not check_vector_db_exists():
        print("✗ 向量数据库不存在")
        return
    
    vector_db = create_vector_db_instance(None)
    question = "什么是去中心化金融？"
    
    # 获取初始检索结果
    docs = vector_db.similarity_search(question, k=5)
    print(f"初始检索到 {len(docs)} 个文档")
    
    reranker_types = ["similarity"]
    
    # 如果有相关依赖，测试交叉编码器
    try:
        from sentence_transformers import CrossEncoder
        reranker_types.append("cross_encoder")
    except ImportError:
        print("  跳过交叉编码器测试（缺少依赖）")
    
    for reranker_type in reranker_types:
        try:
            print(f"\n测试 {reranker_type} 重排序器:")
            reranker = create_reranker(reranker_type)
            
            start_time = time.time()
            reranked_docs = reranker.rerank(question, docs, top_k=3)
            rerank_time = time.time() - start_time
            
            print(f"  ✓ 重排序完成 (用时: {rerank_time:.3f}秒)")
            print(f"  ✓ 返回 {len(reranked_docs)} 个文档")
            
            for i, doc in enumerate(reranked_docs):
                score = doc.metadata.get('rerank_score', 'N/A')
                print(f"    {i+1}. 分数: {score}, 内容: {doc.page_content[:80]}...")
                
        except Exception as e:
            print(f"  ✗ {reranker_type} 重排序器测试失败: {str(e)}")
    
    print()


def main():
    """主测试函数"""
    print("开始重排序功能测试")
    print(f"当前配置:")
    print(f"  重排序启用: {RERANK_ENABLED}")
    print(f"  重排序类型: {RERANK_TYPE}")
    print(f"  返回文档数: {RERANK_TOP_K}")
    print(f"  模型名称: {RERANK_MODEL_NAME}")
    print()
    
    # 运行测试
    test_reranker_creation()
    test_reranker_functionality()
    test_different_reranker_types()
    test_rag_with_reranking()
    
    print("重排序功能测试完成")


if __name__ == "__main__":
    main()

#!/bin/bash

# 设置环境变量
export DAPR_APP_ID=rag-service
export DAPR_APP_PORT=8080
export DAPR_HTTP_PORT=3500
export DAPR_GRPC_PORT=50000
export DEBUG_MODE=True

# 启动 Dapr 服务
dapr run \
  --app-id $DAPR_APP_ID \
  --app-port $DAPR_APP_PORT \
  --dapr-http-port $DAPR_HTTP_PORT \
  --dapr-grpc-port $DAPR_GRPC_PORT \
  --components-path ./dapr/components \
  --config ./dapr/config.yaml \
  -- python rag_service.py 
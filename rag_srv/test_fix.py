#!/usr/bin/env python
"""
测试重排序功能修复
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_reranker_import():
    """测试重排序器导入"""
    print("=== 测试重排序器导入 ===")
    try:
        from reranker import create_reranker
        print("✓ 重排序器模块导入成功")
        
        # 测试创建相似度重排序器
        similarity_reranker = create_reranker("similarity")
        print("✓ 相似度重排序器创建成功")
        
        return True
    except Exception as e:
        print(f"✗ 重排序器导入失败: {str(e)}")
        return False

def test_config_import():
    """测试配置导入"""
    print("\n=== 测试配置导入 ===")
    try:
        from config import (
            RERANK_ENABLED, RERANK_TYPE, RERANK_TOP_K, 
            RERANK_MODEL_NAME, RERANK_MAX_DOCS_FOR_LLM
        )
        print("✓ 重排序配置导入成功")
        print(f"  RERANK_ENABLED: {RERANK_ENABLED}")
        print(f"  RERANK_TYPE: {RERANK_TYPE}")
        print(f"  RERANK_TOP_K: {RERANK_TOP_K}")
        return True
    except Exception as e:
        print(f"✗ 配置导入失败: {str(e)}")
        return False

def test_rag_agent_creation():
    """测试RAG代理创建（不依赖向量数据库）"""
    print("\n=== 测试RAG代理创建逻辑 ===")
    try:
        # 只测试导入，不实际创建
        from rag_agent import create_rag_agent, global_reranker
        print("✓ RAG代理模块导入成功")
        print(f"  全局重排序器初始状态: {global_reranker}")
        return True
    except Exception as e:
        print(f"✗ RAG代理导入失败: {str(e)}")
        return False

def test_service_integration():
    """测试服务集成"""
    print("\n=== 测试服务集成 ===")
    try:
        from rag_service import initialize_rag_agent
        print("✓ 服务模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 服务集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始重排序功能修复测试")
    print("=" * 50)
    
    tests = [
        test_reranker_import,
        test_config_import,
        test_rag_agent_creation,
        test_service_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过，重排序功能修复成功！")
        print("\n建议:")
        print("1. 确保安装了所需依赖: pip install -r requirements.txt")
        print("2. 检查配置文件中的重排序设置")
        print("3. 运行完整的RAG系统测试")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

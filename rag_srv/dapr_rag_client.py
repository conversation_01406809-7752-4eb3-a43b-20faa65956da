import requests
import json
import sys
import os

# Dapr 配置
DAPR_HTTP_PORT = 3500
DAPR_APP_ID = "rag-service"

# 基础 URL
base_url = f"http://localhost:{DAPR_HTTP_PORT}/v1.0/invoke/{DAPR_APP_ID}/method"

def check_status():
    """检查 RAG 服务状态"""
    url = f"{base_url}/status"
    response = requests.get(url)
    return response.json()

def add_document(file_path, title=None, author=None, subject=None):
    """添加单个文档到向量库"""
    url = f"{base_url}/documents"
    data = {"file_path": file_path}
    
    # 添加可选的元数据参数
    if title:
        data["title"] = title
    if author:
        data["author"] = author
    if subject:
        data["subject"] = subject
        
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    return response.json()

def add_batch_documents(file_paths):
    """批量添加文档到向量库"""
    url = f"{base_url}/documents/batch"
    data = {"file_paths": file_paths}
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    return response.json()

def query(question):
    """向 RAG 服务发送查询"""
    url = f"{base_url}/query"
    data = {"question": question}
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    return response.json()

def list_documents():
    """获取向量库中的所有文档列表"""
    url = f"{base_url}/documents"
    response = requests.get(url)
    return response.json()

def get_document(document_id):
    """获取单个文档详情"""
    url = f"{base_url}/documents/{document_id}"
    response = requests.get(url)
    return response.json()

def delete_document(document_id):
    """删除单个文档"""
    url = f"{base_url}/documents/{document_id}"
    response = requests.delete(url)
    return response.json()

def publish_document_update(file_path):
    """通过发布/订阅发送文档更新事件"""
    url = f"http://localhost:{DAPR_HTTP_PORT}/v1.0/publish/pubsub/document-updates"
    data = {"operation": "add", "file_path": file_path}
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    return response.status_code

def print_help():
    """打印帮助信息"""
    print("Dapr RAG 客户端使用说明:")
    print("  python dapr_rag_client.py status - 检查服务状态")
    print("  python dapr_rag_client.py add <文件路径> [--title <标题>] [--author <作者>] [--subject <主题>] - 添加单个文档")
    print("  python dapr_rag_client.py batch-add <文件路径1> <文件路径2> ... - 批量添加文档")
    print("  python dapr_rag_client.py list - 获取所有文档列表")
    print("  python dapr_rag_client.py get <文档ID> - 获取单个文档详情")
    print("  python dapr_rag_client.py delete <文档ID> - 删除单个文档")
    print("  python dapr_rag_client.py query <问题> - 向 RAG 代理发送查询")
    print("  python dapr_rag_client.py publish <文件路径> - 通过发布/订阅发送文档更新事件")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print_help()
        sys.exit(1)

    command = sys.argv[1]

    if command == "status":
        result = check_status()
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "add" and len(sys.argv) >= 3:
        file_path = sys.argv[2]
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            sys.exit(1)
            
        # 解析可选参数
        title = None
        author = None
        subject = None
        
        i = 3
        while i < len(sys.argv):
            if sys.argv[i] == "--title" and i + 1 < len(sys.argv):
                title = sys.argv[i + 1]
                i += 2
            elif sys.argv[i] == "--author" and i + 1 < len(sys.argv):
                author = sys.argv[i + 1]
                i += 2
            elif sys.argv[i] == "--subject" and i + 1 < len(sys.argv):
                subject = sys.argv[i + 1]
                i += 2
            else:
                i += 1
                
        result = add_document(file_path, title, author, subject)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "batch-add" and len(sys.argv) >= 3:
        file_paths = sys.argv[2:]
        for path in file_paths:
            if not os.path.exists(path):
                print(f"错误: 文件不存在 - {path}")
                sys.exit(1)
        result = add_batch_documents(file_paths)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "list":
        result = list_documents()
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "get" and len(sys.argv) >= 3:
        document_id = sys.argv[2]
        result = get_document(document_id)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "delete" and len(sys.argv) >= 3:
        document_id = sys.argv[2]
        result = delete_document(document_id)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "query" and len(sys.argv) >= 3:
        question = " ".join(sys.argv[2:])
        result = query(question)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "publish" and len(sys.argv) >= 3:
        file_path = sys.argv[2]
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            sys.exit(1)
        status_code = publish_document_update(file_path)
        if status_code == 204:
            print("事件发布成功")
        else:
            print(f"事件发布失败，状态码: {status_code}")

    else:
        print_help()
        sys.exit(1) 
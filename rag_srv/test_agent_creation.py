#!/usr/bin/env python
"""
测试RAG代理创建是否修复
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基础导入"""
    print("=== 测试基础导入 ===")
    try:
        from config import RERANK_ENABLED, RERANK_TYPE
        print(f"✓ 配置导入成功: RERANK_ENABLED={RERANK_ENABLED}, RERANK_TYPE={RERANK_TYPE}")
        
        from reranker import create_reranker
        print("✓ 重排序器导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 基础导入失败: {str(e)}")
        return False

def test_wrapper_class():
    """测试包装类"""
    print("\n=== 测试包装类 ===")
    try:
        from rag_agent import RAGAgentWrapper
        
        # 创建一个模拟的链对象
        class MockChain:
            def invoke(self, input_data):
                return {"answer": "test response"}
            
            def with_config(self, config):
                return self
        
        # 测试包装类
        mock_chain = MockChain()
        wrapper = RAGAgentWrapper(mock_chain, None)
        
        # 测试代理调用
        result = wrapper.invoke({"input": "test"})
        print(f"✓ 包装类工作正常: {result}")
        
        return True
    except Exception as e:
        print(f"✗ 包装类测试失败: {str(e)}")
        return False

def test_reranker_creation():
    """测试重排序器创建"""
    print("\n=== 测试重排序器创建 ===")
    try:
        from reranker import create_reranker
        
        # 测试相似度重排序器
        similarity_reranker = create_reranker("similarity")
        print("✓ 相似度重排序器创建成功")
        
        # 测试重排序功能
        class MockDoc:
            def __init__(self, content):
                self.page_content = content
                self.metadata = {}
        
        docs = [
            MockDoc("Python是编程语言"),
            MockDoc("机器学习很有趣"),
            MockDoc("数据科学应用广泛")
        ]
        
        reranked = similarity_reranker.rerank("Python编程", docs, 2)
        print(f"✓ 重排序功能正常: 返回{len(reranked)}个文档")
        
        return True
    except Exception as e:
        print(f"✗ 重排序器创建失败: {str(e)}")
        return False

def test_global_reranker():
    """测试全局重排序器设置"""
    print("\n=== 测试全局重排序器 ===")
    try:
        from rag_agent import global_reranker
        print(f"✓ 全局重排序器访问成功: {global_reranker}")
        
        # 测试设置全局重排序器
        from reranker import create_reranker
        test_reranker = create_reranker("similarity")
        
        # 模拟设置全局变量
        import rag_agent
        rag_agent.global_reranker = test_reranker
        print("✓ 全局重排序器设置成功")
        
        return True
    except Exception as e:
        print(f"✗ 全局重排序器测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始RAG代理创建修复测试")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_wrapper_class,
        test_reranker_creation,
        test_global_reranker
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过，RAG代理创建修复成功！")
        print("\n修复说明:")
        print("1. 使用RAGAgentWrapper包装类避免直接修改LangChain对象")
        print("2. 通过全局变量global_reranker存储重排序器")
        print("3. 在user_query_with_rerank中正确使用重排序器")
        print("\n现在可以安全地运行rag_agent.py了")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

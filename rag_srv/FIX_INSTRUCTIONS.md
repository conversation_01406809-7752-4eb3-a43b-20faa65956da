# 重排序功能错误修复说明

## 问题描述

在远程服务器上运行时出现错误：
```
ValueError: "RunnableBinding" object has no field "reranker"
```

## 错误原因

原代码试图直接给LangChain的链对象添加`reranker`属性：
```python
rag_chain.reranker = reranker  # 这行代码导致错误
```

LangChain的对象是Pydantic模型，不允许动态添加属性。

## 修复方案

已经实施了以下修复：

### 1. 使用全局变量存储重排序器

```python
# 在rag_agent.py中
global_reranker = None

def create_rag_agent(vector_db, debug_mode=True):
    global global_reranker
    # ... 创建重排序器
    global_reranker = reranker
    # ... 
    return rag_chain  # 直接返回原始链，不添加属性
```

### 2. 修改查询函数使用全局重排序器

```python
def user_query_with_rerank(agent, question, vector_db, debug_mode=True):
    # 检查是否启用重排序
    reranker_to_use = None
    if hasattr(agent, 'reranker') and agent.reranker:
        reranker_to_use = agent.reranker
    elif global_reranker:
        reranker_to_use = global_reranker
    
    if RERANK_ENABLED and reranker_to_use:
        # 使用重排序器
        reranked_docs = reranker_to_use.rerank(question, docs, RERANK_TOP_K)
```

## 验证修复

### 1. 检查配置
确保在`config.py`中正确设置：
```python
RERANK_ENABLED = True
RERANK_TYPE = "similarity"  # 或其他支持的类型
RERANK_TOP_K = 3
```

### 2. 运行测试
```bash
cd rag_srv
python3 test_agent_creation.py
```

### 3. 启动服务
```bash
python3 rag_agent.py
```

应该不再出现`"RunnableBinding" object has no field "reranker"`错误。

## 重排序功能使用

### 配置选项

在`config.py`或环境变量中设置：

```python
# 启用重排序
RERANK_ENABLED = True

# 选择重排序算法
RERANK_TYPE = "similarity"      # 基于相似度（推荐，无需额外依赖）
# RERANK_TYPE = "cross_encoder"  # 基于交叉编码器（需要安装sentence-transformers）
# RERANK_TYPE = "llm"           # 基于LLM（计算成本高）
# RERANK_TYPE = "hybrid"        # 混合方法

# 重排序参数
RERANK_TOP_K = 3                # 返回前3个最相关文档
RERANK_MODEL_NAME = "cross-encoder/ms-marco-MiniLM-L-6-v2"  # 交叉编码器模型
RERANK_MAX_DOCS_FOR_LLM = 10    # LLM重排序的最大文档数
```

### API使用

重排序功能会自动集成到现有的查询接口：

```bash
curl -X POST "http://localhost:8000/query" \
     -H "Content-Type: application/json" \
     -d '{"question": "什么是机器学习？"}'
```

### 程序化使用

```python
from rag_agent import user_query_with_rerank, create_rag_agent, create_vector_db_instance

# 创建向量数据库和RAG代理
vector_db = create_vector_db_instance(None)
agent = create_rag_agent(vector_db)

# 使用重排序查询
result = user_query_with_rerank(agent, "什么是DeFi？", vector_db)
print(result["output"])
```

## 性能优化建议

1. **算法选择**：
   - 快速部署：使用`similarity`
   - 平衡效果：使用`cross_encoder`（需要安装依赖）
   - 最高质量：使用`llm`或`hybrid`

2. **参数调优**：
   - 初始检索：k=10（在create_rag_agent中设置）
   - 重排序返回：RERANK_TOP_K=3-5
   - 根据响应时间和质量需求调整

3. **依赖安装**（可选）：
   ```bash
   # 如果使用cross_encoder重排序
   pip install sentence-transformers torch transformers
   ```

## 故障排除

### 常见问题

1. **ImportError相关**：
   - 使用`similarity`重排序（无需额外依赖）
   - 或安装相应依赖包

2. **重排序效果不明显**：
   - 检查`RERANK_TOP_K`设置
   - 确保向量数据库有足够文档
   - 尝试不同重排序算法

3. **性能问题**：
   - 使用`similarity`重排序提高速度
   - 减少`RERANK_TOP_K`值
   - 调整初始检索文档数量

### 调试模式

启用调试查看详细信息：
```python
DEBUG_MODE = True
```

## 总结

修复后的重排序功能：
- ✅ 避免了LangChain对象属性错误
- ✅ 保持了完整的重排序功能
- ✅ 支持多种重排序算法
- ✅ 提供灵活的配置选项
- ✅ 向后兼容现有代码

现在可以安全地在远程服务器上运行RAG系统，并享受重排序功能带来的回答质量提升！

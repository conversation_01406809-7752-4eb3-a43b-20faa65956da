#!/usr/bin/env python
"""
RAG重排序模块
支持多种重排序算法来提高检索文档的相关性
"""

import os
import json
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from abc import ABC, abstractmethod
import logging

# 尝试导入可选依赖
try:
    from sentence_transformers import SentenceTransformer, CrossEncoder
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("警告: sentence-transformers未安装，交叉编码器重排序将不可用")

try:
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("警告: scikit-learn未安装，基于相似度的重排序将不可用")

try:
    from langchain.schema import Document
except ImportError:
    from langchain_core.documents import Document


class BaseReranker(ABC):
    """重排序器基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"reranker.{name}")
    
    @abstractmethod
    def rerank(self, query: str, documents: List[Document], top_k: Optional[int] = None) -> List[Document]:
        """
        重排序文档
        
        Args:
            query: 用户查询
            documents: 待重排序的文档列表
            top_k: 返回前k个文档，如果为None则返回所有文档
            
        Returns:
            重排序后的文档列表
        """
        pass


class SimilarityReranker(BaseReranker):
    """基于相似度分数的重排序器"""
    
    def __init__(self):
        super().__init__("similarity")
        if not SKLEARN_AVAILABLE:
            raise ImportError("scikit-learn is required for SimilarityReranker")
    
    def rerank(self, query: str, documents: List[Document], top_k: Optional[int] = None) -> List[Document]:
        """基于现有相似度分数进行重排序"""
        self.logger.info(f"使用相似度重排序，文档数量: {len(documents)}")
        
        # 如果文档有相似度分数元数据，使用它们进行排序
        scored_docs = []
        for doc in documents:
            # 尝试从元数据中获取相似度分数
            score = doc.metadata.get('similarity_score', 0.0)
            if score == 0.0:
                # 如果没有分数，使用简单的文本匹配分数
                score = self._calculate_text_similarity(query, doc.page_content)
            scored_docs.append((doc, score))
        
        # 按分数降序排序
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        
        # 返回重排序后的文档
        reranked_docs = [doc for doc, score in scored_docs]
        
        if top_k:
            reranked_docs = reranked_docs[:top_k]
        
        self.logger.info(f"重排序完成，返回文档数量: {len(reranked_docs)}")
        return reranked_docs
    
    def _calculate_text_similarity(self, query: str, text: str) -> float:
        """计算简单的文本相似度"""
        query_words = set(query.lower().split())
        text_words = set(text.lower().split())
        
        if not query_words or not text_words:
            return 0.0
        
        intersection = query_words.intersection(text_words)
        union = query_words.union(text_words)
        
        return len(intersection) / len(union) if union else 0.0


class CrossEncoderReranker(BaseReranker):
    """基于交叉编码器的重排序器"""
    
    def __init__(self, model_name: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"):
        super().__init__("cross_encoder")
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("sentence-transformers is required for CrossEncoderReranker")
        
        self.model_name = model_name
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载交叉编码器模型"""
        try:
            self.logger.info(f"加载交叉编码器模型: {self.model_name}")
            self.model = CrossEncoder(self.model_name)
            self.logger.info("交叉编码器模型加载成功")
        except Exception as e:
            self.logger.error(f"加载交叉编码器模型失败: {str(e)}")
            raise
    
    def rerank(self, query: str, documents: List[Document], top_k: Optional[int] = None) -> List[Document]:
        """使用交叉编码器进行重排序"""
        if not self.model:
            self.logger.error("交叉编码器模型未加载")
            return documents
        
        self.logger.info(f"使用交叉编码器重排序，文档数量: {len(documents)}")
        
        # 准备查询-文档对
        query_doc_pairs = [(query, doc.page_content) for doc in documents]
        
        try:
            # 计算相关性分数
            scores = self.model.predict(query_doc_pairs)
            
            # 将文档和分数配对
            scored_docs = list(zip(documents, scores))
            
            # 按分数降序排序
            scored_docs.sort(key=lambda x: x[1], reverse=True)
            
            # 更新文档元数据中的重排序分数
            for doc, score in scored_docs:
                doc.metadata['rerank_score'] = float(score)
            
            # 返回重排序后的文档
            reranked_docs = [doc for doc, score in scored_docs]
            
            if top_k:
                reranked_docs = reranked_docs[:top_k]
            
            self.logger.info(f"交叉编码器重排序完成，返回文档数量: {len(reranked_docs)}")
            return reranked_docs
            
        except Exception as e:
            self.logger.error(f"交叉编码器重排序失败: {str(e)}")
            return documents


class LLMReranker(BaseReranker):
    """基于LLM的重排序器"""
    
    def __init__(self, llm, max_docs_for_llm: int = 10):
        super().__init__("llm")
        self.llm = llm
        self.max_docs_for_llm = max_docs_for_llm
    
    def rerank(self, query: str, documents: List[Document], top_k: Optional[int] = None) -> List[Document]:
        """使用LLM进行重排序"""
        self.logger.info(f"使用LLM重排序，文档数量: {len(documents)}")
        
        # 如果文档太多，先用简单方法筛选
        if len(documents) > self.max_docs_for_llm:
            self.logger.info(f"文档数量过多，先用相似度筛选到{self.max_docs_for_llm}个")
            similarity_reranker = SimilarityReranker()
            documents = similarity_reranker.rerank(query, documents, self.max_docs_for_llm)
        
        try:
            # 构建重排序提示
            prompt = self._build_rerank_prompt(query, documents)
            
            # 调用LLM
            response = self.llm.invoke(prompt)
            
            # 解析LLM响应
            reranked_docs = self._parse_llm_response(response, documents)
            
            if top_k:
                reranked_docs = reranked_docs[:top_k]
            
            self.logger.info(f"LLM重排序完成，返回文档数量: {len(reranked_docs)}")
            return reranked_docs
            
        except Exception as e:
            self.logger.error(f"LLM重排序失败: {str(e)}")
            return documents
    
    def _build_rerank_prompt(self, query: str, documents: List[Document]) -> str:
        """构建重排序提示"""
        prompt = f"""请根据查询的相关性对以下文档进行重排序。

查询: {query}

文档列表:
"""
        for i, doc in enumerate(documents):
            content_preview = doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content
            prompt += f"{i+1}. {content_preview}\n\n"
        
        prompt += """请按照与查询最相关的顺序返回文档编号，用逗号分隔。例如: 3,1,5,2,4

重排序结果:"""
        
        return prompt
    
    def _parse_llm_response(self, response: str, documents: List[Document]) -> List[Document]:
        """解析LLM响应"""
        try:
            # 提取响应文本
            if hasattr(response, 'content'):
                response_text = response.content
            elif isinstance(response, str):
                response_text = response
            else:
                response_text = str(response)
            
            # 解析文档编号
            numbers = [int(x.strip()) for x in response_text.split(',') if x.strip().isdigit()]
            
            # 重排序文档
            reranked_docs = []
            used_indices = set()
            
            for num in numbers:
                if 1 <= num <= len(documents) and (num-1) not in used_indices:
                    reranked_docs.append(documents[num-1])
                    used_indices.add(num-1)
            
            # 添加未被选中的文档
            for i, doc in enumerate(documents):
                if i not in used_indices:
                    reranked_docs.append(doc)
            
            return reranked_docs
            
        except Exception as e:
            self.logger.error(f"解析LLM响应失败: {str(e)}")
            return documents


class HybridReranker(BaseReranker):
    """混合重排序器，结合多种方法"""
    
    def __init__(self, rerankers: List[BaseReranker], weights: Optional[List[float]] = None):
        super().__init__("hybrid")
        self.rerankers = rerankers
        self.weights = weights or [1.0] * len(rerankers)
        
        if len(self.weights) != len(self.rerankers):
            raise ValueError("权重数量必须与重排序器数量相同")
    
    def rerank(self, query: str, documents: List[Document], top_k: Optional[int] = None) -> List[Document]:
        """使用多个重排序器进行混合重排序"""
        self.logger.info(f"使用混合重排序，重排序器数量: {len(self.rerankers)}")
        
        # 收集所有重排序器的分数
        all_scores = {}
        
        for i, reranker in enumerate(self.rerankers):
            try:
                reranked_docs = reranker.rerank(query, documents)
                
                # 计算位置分数（排名越靠前分数越高）
                for rank, doc in enumerate(reranked_docs):
                    doc_id = id(doc)  # 使用文档对象ID作为唯一标识
                    if doc_id not in all_scores:
                        all_scores[doc_id] = {'doc': doc, 'scores': []}
                    
                    # 位置分数：第一名得分最高
                    position_score = (len(reranked_docs) - rank) / len(reranked_docs)
                    all_scores[doc_id]['scores'].append(position_score * self.weights[i])
                    
            except Exception as e:
                self.logger.error(f"重排序器 {reranker.name} 失败: {str(e)}")
        
        # 计算加权平均分数
        final_scores = []
        for doc_id, data in all_scores.items():
            avg_score = sum(data['scores']) / len(data['scores']) if data['scores'] else 0.0
            final_scores.append((data['doc'], avg_score))
        
        # 按最终分数排序
        final_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 返回重排序后的文档
        reranked_docs = [doc for doc, score in final_scores]
        
        if top_k:
            reranked_docs = reranked_docs[:top_k]
        
        self.logger.info(f"混合重排序完成，返回文档数量: {len(reranked_docs)}")
        return reranked_docs


def create_reranker(reranker_type: str, **kwargs) -> BaseReranker:
    """创建重排序器工厂函数"""
    if reranker_type == "similarity":
        return SimilarityReranker()
    elif reranker_type == "cross_encoder":
        model_name = kwargs.get("model_name", "cross-encoder/ms-marco-MiniLM-L-6-v2")
        return CrossEncoderReranker(model_name)
    elif reranker_type == "llm":
        llm = kwargs.get("llm")
        if not llm:
            raise ValueError("LLM重排序器需要提供llm参数")
        max_docs = kwargs.get("max_docs_for_llm", 10)
        return LLMReranker(llm, max_docs)
    elif reranker_type == "hybrid":
        rerankers = kwargs.get("rerankers", [])
        weights = kwargs.get("weights")
        return HybridReranker(rerankers, weights)
    else:
        raise ValueError(f"不支持的重排序器类型: {reranker_type}")
